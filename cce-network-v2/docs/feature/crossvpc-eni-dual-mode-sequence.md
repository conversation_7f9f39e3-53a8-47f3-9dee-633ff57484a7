# CrossVPCEni 双模式时序图

## 1. 独占模式（CrossVPCPrimary）时序图

```mermaid
sequenceDiagram
    participant Pod as Pod (Primary Mode)
    participant <PERSON><PERSON> as ENIM Plugin
    participant IPAM as CRD Allocator
    participant E<PERSON> as ENI CRD
    participant Primary as Primary ENI Manager
    participant Cloud as ENI Syncer
    participant Node as Node

    Note over Pod,Node: 【独占模式】Pod独占整个CrossVPC ENI
    
    Pod->>CNI: 创建Pod，触发CNI ADD
    CNI->>CNI: 解析注解：mode=primary
    
    Note over CNI,IPAM: 【关键步骤1】IPAM分配（复用Primary逻辑）
    CNI->>IPAM: AllocateIP请求
    IPAM->>IPAM: 检查CrossVPC注解，识别为primary模式
    IPAM->>IPAM: 调用allocateCrossVPCPrimary()
    
    Note over IPAM,ENI: 【关键步骤2】创建独占ENI（复用Primary CRD）
    IPAM->>ENI: 创建ENI(UseMode=CrossVPCPrimary)
    ENI-->>IPAM: 返回ENI对象
    IPAM->>Primary: 注册到Primary管理器
    IPAM-->>CNI: 返回ENI信息
    
    Note over Cloud,Node: 【关键步骤3】ENI创建和附加（复用Primary状态机）
    Cloud->>Cloud: 检测到CrossVPCPrimary ENI
    Cloud->>Cloud: 使用CrossVPC配置创建ENI
    Cloud->>Node: 附加ENI到实例
    Cloud->>ENI: 更新状态为Inuse
    Cloud->>ENI: 设置endpointReference
    
    Note over CNI,Pod: 【关键步骤4】网络配置（复用Primary网络配置）
    CNI->>CNI: 等待ENI状态变为Inuse
    CNI->>Node: 获取ENI设备（通过MAC地址）
    CNI->>Node: 移动ENI设备到容器命名空间
    CNI->>Node: 配置IP地址（ENI主IP）
    CNI->>Node: 配置CrossVPC路由规则
    CNI->>Pod: 返回网络配置
```

## 2. 共享模式（CrossVPCSecondary）时序图

```mermaid
sequenceDiagram
    participant Pod1 as Pod1 (Secondary Mode)
    participant Pod2 as Pod2 (Secondary Mode)
    participant CNI as ENIM Plugin
    participant IPAM as CRD Allocator
    participant ENI as ENI CRD
    participant NRS as NetResourceSet
    participant Cloud as ENI Syncer
    participant Node as Node

    Note over Pod1,Node: 【共享模式】多个Pod共享一个CrossVPC ENI的不同IP
    
    Pod1->>CNI: 创建Pod1，触发CNI ADD
    CNI->>CNI: 解析注解：mode=secondary
    
    Note over CNI,IPAM: 【关键步骤1】IPAM分配（复用Secondary逻辑）
    CNI->>IPAM: AllocateIP请求
    IPAM->>IPAM: 检查CrossVPC注解，识别为secondary模式
    IPAM->>IPAM: 调用allocateCrossVPCSecondary()
    
    Note over IPAM,ENI: 【关键步骤2】查找或创建共享ENI
    IPAM->>ENI: 查找现有CrossVPCSecondary ENI
    ENI-->>IPAM: 未找到可用ENI
    IPAM->>ENI: 创建ENI(UseMode=CrossVPCSecondary)
    ENI-->>IPAM: 返回新ENI对象
    
    Note over IPAM,NRS: 【关键步骤3】IP池管理（复用Secondary IP池逻辑）
    IPAM->>NRS: 将ENI添加到CrossVPC IP池
    IPAM->>NRS: 从IP池分配辅助IP给Pod1
    NRS-->>IPAM: 返回分配的IP
    IPAM-->>CNI: 返回IP信息
    
    Note over Cloud,Node: 【关键步骤4】ENI创建和IP分配（复用Secondary状态机）
    Cloud->>Cloud: 检测到CrossVPCSecondary ENI
    Cloud->>Cloud: 使用CrossVPC配置创建ENI
    Cloud->>Node: 附加ENI到实例
    Cloud->>Cloud: 为ENI分配辅助IP
    Cloud->>ENI: 更新ENI状态和IP列表
    
    Note over CNI,Pod1: 【关键步骤5】网络配置（复用Secondary网络配置）
    CNI->>CNI: 等待IP分配完成
    CNI->>Node: 创建veth pair或IPVLAN设备
    CNI->>Node: 配置分配的辅助IP
    CNI->>Node: 配置CrossVPC路由规则
    CNI->>Pod1: 返回网络配置
    
    Note over Pod2,Node: 【第二个Pod复用同一个ENI】
    Pod2->>CNI: 创建Pod2，触发CNI ADD
    CNI->>IPAM: AllocateIP请求
    IPAM->>ENI: 查找现有CrossVPCSecondary ENI
    ENI-->>IPAM: 找到可用ENI
    IPAM->>NRS: 从同一IP池分配新的辅助IP给Pod2
    NRS-->>IPAM: 返回新分配的IP
    
    Cloud->>Cloud: 为ENI添加新的辅助IP
    Cloud->>ENI: 更新ENI的IP列表
    
    CNI->>Node: 为Pod2配置新的辅助IP
    CNI->>Pod2: 返回网络配置
```

## 3. 模式对比流程图

```mermaid
flowchart TD
    A[Pod创建] --> B{解析CrossVPC注解}
    B -->|无注解| C[常规ENI处理]
    B -->|有注解| D{检查mode参数}
    
    D -->|mode=primary| E[独占模式处理]
    D -->|mode=secondary| F[共享模式处理]
    D -->|未指定| G[默认共享模式]
    
    E --> E1[创建独占ENI]
    E1 --> E2[复用Primary管理器]
    E2 --> E3[ENI设备移动到容器]
    E3 --> E4[配置主IP和路由]
    
    F --> F1[查找或创建共享ENI]
    F1 --> F2[复用Secondary IP池]
    F2 --> F3[分配辅助IP]
    F3 --> F4[创建veth/IPVLAN设备]
    F4 --> F5[配置辅助IP和路由]
    
    G --> F1
    
    E4 --> H[配置CrossVPC路由]
    F5 --> H
    H --> I[Pod网络就绪]
    
    style E fill:#ffebee
    style F fill:#e8f5e8
    style E1 fill:#ffcdd2
    style E2 fill:#ffcdd2
    style E3 fill:#ffcdd2
    style E4 fill:#ffcdd2
    style F1 fill:#c8e6c9
    style F2 fill:#c8e6c9
    style F3 fill:#c8e6c9
    style F4 fill:#c8e6c9
    style F5 fill:#c8e6c9
```

## 4. 资源生命周期对比

### 4.1 独占模式资源生命周期

```mermaid
stateDiagram-v2
    [*] --> Pending: 创建CrossVPCPrimary ENI
    Pending --> Created: 云平台创建ENI
    Created --> Attaching: 开始附加到实例
    Attaching --> Inuse: 附加完成
    Inuse --> UsingInPod: Pod独占使用
    UsingInPod --> Inuse: Pod删除
    Inuse --> Detaching: 开始分离
    Detaching --> Detached: 分离完成
    Detached --> Deleted: 删除ENI
    Deleted --> [*]
    
    note right of UsingInPod
        独占模式：
        一个ENI只能被一个Pod使用
        Pod删除后ENI可被其他Pod复用
    end note
```

### 4.2 共享模式资源生命周期

```mermaid
stateDiagram-v2
    [*] --> Pending: 创建CrossVPCSecondary ENI
    Pending --> Created: 云平台创建ENI
    Created --> Attaching: 开始附加到实例
    Attaching --> Inuse: 附加完成
    Inuse --> SharedByPods: 多个Pod共享使用
    SharedByPods --> SharedByPods: Pod创建/删除
    SharedByPods --> Inuse: 所有Pod删除
    Inuse --> Detaching: 开始分离（无Pod使用时）
    Detaching --> Detached: 分离完成
    Detached --> Deleted: 删除ENI
    Deleted --> [*]
    
    note right of SharedByPods
        共享模式：
        一个ENI可被多个Pod共享
        只有当所有Pod都删除后
        ENI才会被回收
    end note
```

## 5. 错误处理流程

### 5.1 独占模式错误处理

```mermaid
flowchart TD
    A[独占模式分配] --> B{ENI创建成功?}
    B -->|失败| C[记录错误日志]
    C --> D[清理部分创建的资源]
    D --> E[返回分配失败]
    
    B -->|成功| F{ENI附加成功?}
    F -->|失败| G[删除已创建的ENI]
    G --> C
    
    F -->|成功| H{网络配置成功?}
    H -->|失败| I[分离ENI]
    I --> G
    
    H -->|成功| J[分配成功]
    
    style C fill:#ffcdd2
    style D fill:#ffcdd2
    style E fill:#ffcdd2
    style G fill:#ffcdd2
    style I fill:#ffcdd2
```

### 5.2 共享模式错误处理

```mermaid
flowchart TD
    A[共享模式分配] --> B{找到可用ENI?}
    B -->|否| C{创建新ENI成功?}
    C -->|失败| D[记录错误日志]
    D --> E[返回分配失败]
    
    C -->|成功| F[ENI添加到IP池]
    B -->|是| F
    
    F --> G{IP分配成功?}
    G -->|失败| H[从IP池移除ENI]
    H --> D
    
    G -->|成功| I{网络配置成功?}
    I -->|失败| J[释放已分配的IP]
    J --> H
    
    I -->|成功| K[分配成功]
    
    style D fill:#ffcdd2
    style E fill:#ffcdd2
    style H fill:#ffcdd2
    style J fill:#ffcdd2
```

## 6. 监控指标流程

### 6.1 关键监控点

```mermaid
flowchart LR
    A[Pod创建] --> B[模式识别]
    B --> C{独占模式}
    B --> D{共享模式}
    
    C --> C1[记录独占ENI分配]
    C1 --> C2[记录ENI利用率=100%]
    
    D --> D1[记录共享ENI分配]
    D1 --> D2[记录IP池利用率]
    D2 --> D3[记录ENI利用率]
    
    C2 --> E[记录分配延迟]
    D3 --> E
    E --> F[记录成功/失败计数]
    
    style C1 fill:#ffebee
    style C2 fill:#ffebee
    style D1 fill:#e8f5e8
    style D2 fill:#e8f5e8
    style D3 fill:#e8f5e8
```

## 7. 总结

这个双模式时序图清晰地展示了：

1. **独占模式**：
   - 完全复用Primary模式的分配和管理逻辑
   - Pod独占整个ENI，性能最优
   - 适合对网络性能要求高的场景

2. **共享模式**：
   - 完全复用Secondary模式的IP池管理逻辑
   - 多个Pod共享ENI的不同IP，成本最优
   - 适合对成本敏感的场景

3. **统一管理**：
   - 两种模式使用相同的ENI CRD和管理框架
   - 通过UseMode字段区分处理逻辑
   - 最大化复用现有的成熟代码

通过这种设计，真正实现了"尽可能复用当前逻辑"的目标，为用户提供了灵活的CrossVPCEni解决方案。
