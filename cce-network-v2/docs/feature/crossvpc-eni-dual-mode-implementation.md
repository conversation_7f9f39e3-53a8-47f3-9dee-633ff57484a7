# CrossVPCEni 双模式实现方案

## 1. 方案概述

本方案支持两种CrossVPCEni使用模式，最大化复用现有ENI逻辑：

1. **独占模式（CrossVPCPrimary）**：Pod独占整个CrossVPC ENI，类似现有的Primary模式
2. **共享模式（CrossVPCSecondary）**：多个Pod共享一个CrossVPC ENI的不同辅助IP，类似现有的Secondary模式

## 2. 核心设计思路

### 2.1 复用现有ENI模式架构
- **独占模式**：复用现有的`ENIUseModePrimaryIP`逻辑和架构
- **共享模式**：复用现有的`ENIUseModeSecondaryIP`逻辑和架构
- **统一管理**：通过ENI UseMode区分，使用相同的管理框架

### 2.2 模式映射关系

| CrossVPC模式 | 对应现有模式 | 复用组件 | Pod与ENI关系 |
|-------------|-------------|---------|-------------|
| CrossVPCPrimary | Primary | primaryENIProvider | 1:1独占 |
| CrossVPCSecondary | Secondary | NetResourceSet IPAM | N:1共享 |

## 3. 详细实现方案

### 3.1 ENI CRD扩展

#### 3.1.1 ENIUseMode扩展

```go
// pkg/k8s/apis/cce.baidubce.com/v2/cce_eni_types.go
type ENIUseMode string

const (
    // 现有模式
    ENIUseModeSecondaryIP            ENIUseMode = "Secondary"
    ENIUseModePrimaryIP              ENIUseMode = "Primary"
    ENIUseModePrimaryWithSecondaryIP ENIUseMode = "PrimaryWithSecondaryIP"
    
    // 新增跨VPC模式
    ENIUseModeCrossVPCPrimary        ENIUseMode = "CrossVPCPrimary"    // 独占模式
    ENIUseModeCrossVPCSecondary      ENIUseMode = "CrossVPCSecondary"  // 共享模式
)

// 判断是否为CrossVPC模式
func (mode ENIUseMode) IsCrossVPC() bool {
    return mode == ENIUseModeCrossVPCPrimary || mode == ENIUseModeCrossVPCSecondary
}

// 判断是否为Primary类型（独占）
func (mode ENIUseMode) IsPrimaryType() bool {
    return mode == ENIUseModePrimaryIP || mode == ENIUseModeCrossVPCPrimary
}

// 判断是否为Secondary类型（共享）
func (mode ENIUseMode) IsSecondaryType() bool {
    return mode == ENIUseModeSecondaryIP || mode == ENIUseModeCrossVPCSecondary
}
```

#### 3.1.2 ENI Spec扩展

```go
type ENISpec struct {
    models.ENI `json:",inline"`
    
    // 现有字段
    NodeName                  string     `json:"nodeName"`
    UseMode                   ENIUseMode `json:"useMode"`
    RouteTableOffset          int        `json:"routeTableOffset"`
    InstallSourceBasedRouting bool       `json:"installSourceBasedRouting,omitempty"`
    VPCVersion                int64      `json:"vpcVersion,omitempty"`
    Type                      ENIType    `json:"type,omitempty"`
    BorrowIPCount             int        `json:"borrowIPCount,omitempty"`
    
    // 新增CrossVPC配置
    CrossVPCConfig *CrossVPCConfig `json:"crossVPCConfig,omitempty"`
}

type CrossVPCConfig struct {
    UserID                           string   `json:"userID"`
    VPCCIDR                          string   `json:"vpcCIDR"`
    DefaultRouteInterfaceDelegation  string   `json:"defaultRouteInterfaceDelegation,omitempty"`
    DefaultRouteExcludedCidrs        []string `json:"defaultRouteExcludedCidrs,omitempty"`
    
    // 独占模式特有字段
    InvolvedContainerID              string   `json:"involvedContainerID,omitempty"`
    
    // 共享模式特有字段
    MaxIPsPerENI                     int      `json:"maxIPsPerENI,omitempty"`
    PreAllocateIPs                   int      `json:"preAllocateIPs,omitempty"`
}
```

### 3.2 Pod注解设计

#### 3.2.1 通用注解

```yaml
# 通用CrossVPC注解
cross-vpc-eni.cce.io/enabled: "true"
cross-vpc-eni.cce.io/userID: "user123456"
cross-vpc-eni.cce.io/subnetID: "sbn-abc123def456"
cross-vpc-eni.cce.io/securityGroupIDs: "sg-123456,sg-789012"
cross-vpc-eni.cce.io/vpcCidr: "***********/16"
cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation: "eni"
cross-vpc-eni.cce.io/defaultRouteExcludedCidrs: "10.0.0.0/8,**********/12"
```

#### 3.2.2 模式特定注解

```yaml
# 独占模式特有注解
cross-vpc-eni.cce.io/mode: "primary"  # 或 "exclusive"
cross-vpc-eni.cce.io/privateIPAddress: "*************"  # 指定主IP

# 共享模式特有注解
cross-vpc-eni.cce.io/mode: "secondary"  # 或 "shared"
# 无需指定IP，由IPAM自动分配辅助IP
```

### 3.3 IPAM分配器扩展

#### 3.3.1 统一入口，分模式处理

```go
// pkg/ipam/crd_allocator.go
func (c *crdAllocator) Allocate(ctx context.Context, owner string) (*AllocationResult, error) {
    // 检查是否为CrossVPC请求
    pod, crossVPCConfig, isCrossVPC := c.shouldHandleCrossVPC(ctx, owner)
    if !isCrossVPC {
        return c.allocateRegularEni(ctx, owner)
    }
    
    // 根据模式分发到不同的处理逻辑
    mode := c.determineCrossVPCMode(pod.Annotations)
    switch mode {
    case "primary":
        return c.allocateCrossVPCPrimary(ctx, owner, pod, crossVPCConfig)
    case "secondary":
        return c.allocateCrossVPCSecondary(ctx, owner, pod, crossVPCConfig)
    default:
        return nil, fmt.Errorf("unsupported CrossVPC mode: %s", mode)
    }
}

func (c *crdAllocator) determineCrossVPCMode(annotations map[string]string) string {
    if mode, ok := annotations["cross-vpc-eni.cce.io/mode"]; ok {
        return mode
    }
    // 默认使用共享模式（更经济）
    return "secondary"
}
```

#### 3.3.2 独占模式分配器（复用Primary逻辑）

```go
func (c *crdAllocator) allocateCrossVPCPrimary(ctx context.Context, owner string, pod *v1.Pod, config *CrossVPCConfig) (*AllocationResult, error) {
    // 创建独占CrossVPC ENI
    eni := &ccev2.ENI{
        ObjectMeta: metav1.ObjectMeta{
            Name: generateCrossVPCEniName(pod.Namespace, pod.Name, "primary"),
            Labels: map[string]string{
                "cce.baidubce.com/eni-type":     "crossvpc",
                "cce.baidubce.com/eni-mode":     "primary",
                "cce.baidubce.com/pod-name":     pod.Name,
                "cce.baidubce.com/pod-namespace": pod.Namespace,
            },
        },
        Spec: ccev2.ENISpec{
            ENI: models.ENI{
                SubnetID:         config.SubnetID,
                SecurityGroupIds: config.SecurityGroupIDs,
                // 独占模式：指定主IP
                PrivateIPSet: []*models.PrivateIP{{
                    Primary:          true,
                    PrivateIPAddress: config.PrivateIPAddress,
                }},
            },
            NodeName:       c.nodeName,
            UseMode:        ccev2.ENIUseModeCrossVPCPrimary,  // 独占模式
            CrossVPCConfig: config,
        },
    }
    
    // 创建ENI并等待就绪（复用现有逻辑）
    return c.createAndWaitForENI(ctx, eni)
}
```

#### 3.3.3 共享模式分配器（复用Secondary逻辑）

```go
func (c *crdAllocator) allocateCrossVPCSecondary(ctx context.Context, owner string, pod *v1.Pod, config *CrossVPCConfig) (*AllocationResult, error) {
    // 查找或创建共享的CrossVPC ENI
    eni, err := c.findOrCreateSharedCrossVPCEni(ctx, pod, config)
    if err != nil {
        return nil, err
    }
    
    // 从ENI的辅助IP池中分配IP（复用现有的IP分配逻辑）
    return c.allocateIPFromCrossVPCEni(ctx, eni, owner)
}

func (c *crdAllocator) findOrCreateSharedCrossVPCEni(ctx context.Context, pod *v1.Pod, config *CrossVPCConfig) (*ccev2.ENI, error) {
    // 查找现有的共享CrossVPC ENI
    eniList, err := c.k8sClient.CceV2().ENIs().List(ctx, metav1.ListOptions{
        LabelSelector: fmt.Sprintf(
            "cce.baidubce.com/eni-type=crossvpc,cce.baidubce.com/eni-mode=secondary,cce.baidubce.com/node-name=%s",
            c.nodeName,
        ),
    })
    if err != nil {
        return nil, err
    }
    
    // 查找有可用IP的ENI
    for _, eni := range eniList.Items {
        if c.hasAvailableIPs(&eni, config) {
            return &eni, nil
        }
    }
    
    // 没有可用ENI，创建新的共享ENI
    return c.createSharedCrossVPCEni(ctx, pod, config)
}

func (c *crdAllocator) createSharedCrossVPCEni(ctx context.Context, pod *v1.Pod, config *CrossVPCConfig) (*ccev2.ENI, error) {
    eni := &ccev2.ENI{
        ObjectMeta: metav1.ObjectMeta{
            Name: generateCrossVPCEniName(pod.Namespace, "shared", "secondary"),
            Labels: map[string]string{
                "cce.baidubce.com/eni-type":     "crossvpc",
                "cce.baidubce.com/eni-mode":     "secondary",
                "cce.baidubce.com/node-name":    c.nodeName,
            },
        },
        Spec: ccev2.ENISpec{
            ENI: models.ENI{
                SubnetID:         config.SubnetID,
                SecurityGroupIds: config.SecurityGroupIDs,
                // 共享模式：创建时只有主IP，辅助IP后续动态分配
            },
            NodeName:       c.nodeName,
            UseMode:        ccev2.ENIUseModeCrossVPCSecondary,  // 共享模式
            CrossVPCConfig: config,
        },
    }
    
    createdEni, err := c.k8sClient.CceV2().ENIs().Create(ctx, eni, metav1.CreateOptions{})
    if err != nil {
        return nil, err
    }
    
    // 等待ENI就绪
    return c.waitForENIReady(ctx, createdEni.Name)
}
```

### 3.4 ENI管理器扩展

#### 3.4.1 统一ENI管理器入口

```go
// pkg/enim/enim.go
func NewENIManager(c ipam.Configuration, watcher *watchers.K8sWatcher) ENIManagerServer {
    // 根据IPAM模式和ENI使用模式选择管理器
    if option.Config.IPAM == ipamopt.IPAMVpcEni {
        // 检查是否启用CrossVPC
        if option.Config.EnableCrossVPCEni {
            return newCrossVPCEniManager(option.Config, watcher)
        }
        
        // 现有逻辑
        if option.Config.ENI.UseMode == string(ccev2.ENIUseModePrimaryIP) {
            return newENIEndpointAllocator(option.Config, watcher)
        }
    }
    
    return newDefaultENIManager(option.Config, watcher)
}

func newCrossVPCEniManager(config *option.DaemonConfig, watcher *watchers.K8sWatcher) ENIManagerServer {
    return &crossVPCEniManager{
        primaryManager:   newENIEndpointAllocator(config, watcher),    // 复用Primary管理器
        secondaryManager: newDefaultENIManager(config, watcher),       // 复用Secondary管理器
        watcher:         watcher,
    }
}

type crossVPCEniManager struct {
    primaryManager   ENIManagerServer  // 处理独占模式
    secondaryManager ENIManagerServer  // 处理共享模式
    watcher         *watchers.K8sWatcher
}

func (m *crossVPCEniManager) AllocateENI(ctx context.Context, endpoint *ccev2.ObjectReference) (*ccev2.ENI, error) {
    // 根据ENI的UseMode分发到不同的管理器
    eni, err := m.getENIByEndpoint(ctx, endpoint)
    if err != nil {
        return nil, err
    }
    
    if eni.Spec.UseMode == ccev2.ENIUseModeCrossVPCPrimary {
        return m.primaryManager.AllocateENI(ctx, endpoint)
    } else if eni.Spec.UseMode == ccev2.ENIUseModeCrossVPCSecondary {
        return m.secondaryManager.AllocateENI(ctx, endpoint)
    }
    
    return nil, fmt.Errorf("unsupported CrossVPC ENI mode: %s", eni.Spec.UseMode)
}
```

### 3.5 CNI插件扩展

#### 3.5.1 统一CNI插件入口

```go
// plugins/enim/enim.go
func cmdAdd(args *skel.CmdArgs) error {
    // 现有初始化逻辑...
    
    // 检查是否为CrossVPC请求
    if isCrossVPCRequest(args) {
        return handleCrossVPCAdd(args)
    }
    
    // 现有处理逻辑...
    return handleRegularAdd(args)
}

func handleCrossVPCAdd(args *skel.CmdArgs) error {
    // 分配ENI（复用现有IPAM接口）
    ipam, releaseFunc, err := allocateENIWithCCEAgent(client, cfg, args.ContainerID, args.Netns)
    if err != nil {
        return err
    }
    defer releaseFunc()
    
    // 根据ENI模式配置网络
    mode := determineCrossVPCModeFromIPAM(ipam)
    switch mode {
    case "primary":
        return configureCrossVPCPrimaryNetwork(args, ipam, cfg)
    case "secondary":
        return configureCrossVPCSecondaryNetwork(args, ipam, cfg)
    default:
        return fmt.Errorf("unsupported CrossVPC mode: %s", mode)
    }
}

func configureCrossVPCPrimaryNetwork(args *skel.CmdArgs, ipam *models.IPAMResponse, cfg *NetConf) error {
    // 独占模式：复用Primary模式的网络配置逻辑
    // 1. 获取ENI设备
    // 2. 移动到容器命名空间
    // 3. 配置IP和路由
    // 4. 配置CrossVPC特有的路由规则
    return configurePrimaryNetworkWithCrossVPCRoutes(args, ipam, cfg)
}

func configureCrossVPCSecondaryNetwork(args *skel.CmdArgs, ipam *models.IPAMResponse, cfg *NetConf) error {
    // 共享模式：复用Secondary模式的网络配置逻辑
    // 1. 创建veth pair或使用IPVLAN
    // 2. 配置IP和路由
    // 3. 配置CrossVPC特有的路由规则
    return configureSecondaryNetworkWithCrossVPCRoutes(args, ipam, cfg)
}
```

## 4. 配置示例

### 4.1 独占模式Pod配置

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: crossvpc-primary-pod
  annotations:
    cross-vpc-eni.cce.io/enabled: "true"
    cross-vpc-eni.cce.io/mode: "primary"  # 独占模式
    cross-vpc-eni.cce.io/userID: "user123456"
    cross-vpc-eni.cce.io/subnetID: "sbn-abc123def456"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123456,sg-789012"
    cross-vpc-eni.cce.io/vpcCidr: "***********/16"
    cross-vpc-eni.cce.io/privateIPAddress: "*************"  # 指定IP
    cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation: "eni"
spec:
  containers:
  - name: app
    image: nginx:latest
```

### 4.2 共享模式Pod配置

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: crossvpc-secondary-pod
  annotations:
    cross-vpc-eni.cce.io/enabled: "true"
    cross-vpc-eni.cce.io/mode: "secondary"  # 共享模式
    cross-vpc-eni.cce.io/userID: "user123456"
    cross-vpc-eni.cce.io/subnetID: "sbn-abc123def456"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123456,sg-789012"
    cross-vpc-eni.cce.io/vpcCidr: "***********/16"
    # 无需指定IP，自动分配辅助IP
    cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation: "eni"
spec:
  containers:
  - name: app
    image: nginx:latest
```

## 5. 资源示例

### 5.1 独占模式ENI资源

```yaml
apiVersion: cce.baidubce.com/v2
kind: ENI
metadata:
  name: crossvpc-primary-pod-abc123
  labels:
    cce.baidubce.com/eni-type: "crossvpc"
    cce.baidubce.com/eni-mode: "primary"
spec:
  nodeName: "worker-node-1"
  useMode: "CrossVPCPrimary"  # 独占模式
  crossVPCConfig:
    userID: "user123456"
    vpcCIDR: "***********/16"
    involvedContainerID: "container-abc123"
status:
  endpointReference:  # 独占模式特有
    namespace: "default"
    name: "crossvpc-primary-pod"
    uid: "pod-uid-123"
```

### 5.2 共享模式ENI资源

```yaml
apiVersion: cce.baidubce.com/v2
kind: ENI
metadata:
  name: crossvpc-shared-secondary-def456
  labels:
    cce.baidubce.com/eni-type: "crossvpc"
    cce.baidubce.com/eni-mode: "secondary"
spec:
  nodeName: "worker-node-1"
  useMode: "CrossVPCSecondary"  # 共享模式
  crossVPCConfig:
    userID: "user123456"
    vpcCIDR: "***********/16"
    maxIPsPerENI: 8
    preAllocateIPs: 2
status:
  # 无endpointReference，多个Pod共享
```

## 6. 实施优势

### 6.1 最大化复用现有逻辑
- **独占模式**：90%复用Primary模式逻辑
- **共享模式**：90%复用Secondary模式逻辑
- **统一管理**：使用相同的ENI CRD和管理框架

### 6.2 灵活的使用方式
- **成本优化**：共享模式节省ENI资源
- **性能优化**：独占模式提供更好的网络性能
- **场景适配**：用户可根据需求选择合适的模式

### 6.3 平滑的迁移路径
- **渐进部署**：可以先支持一种模式，再扩展另一种
- **兼容性保证**：不影响现有ENI功能
- **运维一致性**：使用相同的工具和流程

## 7. 关键技术实现细节

### 7.1 NetResourceSet扩展（共享模式）

```go
// pkg/k8s/apis/cce.baidubce.com/v2/net_resource_set_types.go
type NetResourceSetSpec struct {
    // 现有字段...
    ENI ENISpec `json:"eni,omitempty"`

    // 新增CrossVPC ENI配置
    CrossVPCENI *CrossVPCENISpec `json:"crossVPCENI,omitempty"`
}

type CrossVPCENISpec struct {
    // 复用现有ENI配置结构
    ENISpec `json:",inline"`

    // CrossVPC特有配置
    CrossVPCSubnets []CrossVPCSubnet `json:"crossVPCSubnets,omitempty"`
}

type CrossVPCSubnet struct {
    UserID           string   `json:"userID"`
    SubnetID         string   `json:"subnetID"`
    VPCCIDR          string   `json:"vpcCIDR"`
    SecurityGroupIDs []string `json:"securityGroupIDs"`
    MaxENIs          int      `json:"maxENIs,omitempty"`
    MaxIPsPerENI     int      `json:"maxIPsPerENI,omitempty"`
}
```

### 7.2 IPAM池管理扩展

```go
// pkg/ipam/net_resource.go - 扩展现有的IP池管理
func (n *NetResourceSet) allocateCrossVPCIP(ctx context.Context, owner string, crossVPCConfig *CrossVPCConfig) (*AllocationResult, error) {
    // 查找或创建CrossVPC ENI池
    crossVPCPool := n.getCrossVPCPool(crossVPCConfig)
    if crossVPCPool == nil {
        // 创建新的CrossVPC ENI
        eni, err := n.createCrossVPCENI(ctx, crossVPCConfig)
        if err != nil {
            return nil, err
        }
        crossVPCPool = n.addCrossVPCENIToPool(eni)
    }

    // 从池中分配IP（复用现有的IP分配逻辑）
    return n.allocateIPFromPool(ctx, crossVPCPool, owner)
}

func (n *NetResourceSet) getCrossVPCPool(config *CrossVPCConfig) *ENIPool {
    // 根据CrossVPC配置查找匹配的ENI池
    for _, pool := range n.crossVPCPools {
        if pool.matchesCrossVPCConfig(config) {
            return pool
        }
    }
    return nil
}
```

### 7.3 ENI状态同步扩展

```go
// pkg/bce/bcesync/eni.go - 扩展现有的ENI同步逻辑
func (esm *eniStateMachine) handleCrossVPCENI() error {
    if !esm.resource.Spec.UseMode.IsCrossVPC() {
        return esm.handleRegularENI() // 现有逻辑
    }

    // CrossVPC ENI特殊处理
    switch esm.resource.Spec.UseMode {
    case ccev2.ENIUseModeCrossVPCPrimary:
        return esm.handleCrossVPCPrimary()
    case ccev2.ENIUseModeCrossVPCSecondary:
        return esm.handleCrossVPCSecondary()
    default:
        return fmt.Errorf("unsupported CrossVPC mode: %s", esm.resource.Spec.UseMode)
    }
}

func (esm *eniStateMachine) handleCrossVPCPrimary() error {
    // 复用Primary模式的状态机逻辑
    // 添加CrossVPC特有的处理
    if err := esm.validateCrossVPCConfig(); err != nil {
        return err
    }

    return esm.handlePrimaryENI() // 复用现有Primary逻辑
}

func (esm *eniStateMachine) handleCrossVPCSecondary() error {
    // 复用Secondary模式的状态机逻辑
    // 添加CrossVPC特有的处理
    if err := esm.validateCrossVPCConfig(); err != nil {
        return err
    }

    return esm.handleSecondaryENI() // 复用现有Secondary逻辑
}
```

### 7.4 网络配置路由扩展

```go
// plugins/enim/crossvpc_routes.go
func configureCrossVPCRoutes(args *skel.CmdArgs, ipam *models.IPAMResponse, mode string) error {
    crossVPCConfig := ipam.CrossVPCConfig
    if crossVPCConfig == nil {
        return nil
    }

    // 进入容器网络命名空间
    netns, err := ns.GetNS(args.Netns)
    if err != nil {
        return err
    }
    defer netns.Close()

    return netns.Do(func(_ ns.NetNS) error {
        // 配置CrossVPC特有的路由规则
        if err := configureDefaultRouteDelegate(crossVPCConfig, ipam.Address.Gateway); err != nil {
            return err
        }

        if err := configureExcludedRoutes(crossVPCConfig); err != nil {
            return err
        }

        // 根据模式配置特定路由
        if mode == "primary" {
            return configurePrimaryModeRoutes(crossVPCConfig, ipam)
        } else {
            return configureSecondaryModeRoutes(crossVPCConfig, ipam)
        }
    })
}

func configureDefaultRouteDelegate(config *CrossVPCConfig, gateway net.IP) error {
    if config.DefaultRouteInterfaceDelegation != "eni" {
        return nil
    }

    // 配置默认路由委托给ENI
    defaultRoute := &netlink.Route{
        Dst: nil, // 默认路由
        Gw:  gateway,
    }

    return netlink.RouteAdd(defaultRoute)
}

func configureExcludedRoutes(config *CrossVPCConfig) error {
    for _, excludedCidr := range config.DefaultRouteExcludedCidrs {
        _, cidr, err := net.ParseCIDR(strings.TrimSpace(excludedCidr))
        if err != nil {
            log.WithError(err).Warnf("Invalid excluded CIDR: %s", excludedCidr)
            continue
        }

        // 为排除的CIDR配置特定路由
        excludeRoute := &netlink.Route{
            Dst:   cidr,
            Scope: netlink.SCOPE_LINK,
        }

        if err := netlink.RouteAdd(excludeRoute); err != nil && !os.IsExist(err) {
            log.WithError(err).Warnf("Failed to add exclude route for %s", excludedCidr)
        }
    }
    return nil
}
```

### 7.5 监控指标扩展

```go
// pkg/metrics/eni_metrics.go
var (
    crossVPCEniAllocationTotal = prometheus.NewCounterVec(
        prometheus.CounterOpts{
            Name: "cce_crossvpc_eni_allocation_total",
            Help: "Total number of CrossVPC ENI allocations",
        },
        []string{"node", "mode", "user_id", "vpc_cidr"},
    )

    crossVPCEniUtilization = prometheus.NewGaugeVec(
        prometheus.GaugeOpts{
            Name: "cce_crossvpc_eni_utilization",
            Help: "CrossVPC ENI utilization ratio",
        },
        []string{"node", "mode", "eni_id"},
    )
)

func RecordCrossVPCEniAllocation(node, mode, userID, vpcCIDR string) {
    crossVPCEniAllocationTotal.WithLabelValues(node, mode, userID, vpcCIDR).Inc()
}

func UpdateCrossVPCEniUtilization(node, mode, eniID string, utilization float64) {
    crossVPCEniUtilization.WithLabelValues(node, mode, eniID).Set(utilization)
}
```

## 8. 部署配置

### 8.1 ConfigMap配置扩展

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-network-v2-config
data:
  # 现有配置
  ipam: "vpc-eni"

  # CrossVPC功能开关
  enable-crossvpc-eni: "true"

  # CrossVPC默认模式
  crossvpc-eni-default-mode: "secondary"  # primary | secondary

  # CrossVPC超时配置
  crossvpc-eni-allocation-timeout: "300s"
  crossvpc-eni-gc-interval: "5m"

  # CrossVPC共享模式配置
  crossvpc-eni-max-ips-per-eni: "8"
  crossvpc-eni-pre-allocate-ips: "2"

  # CrossVPC独占模式配置
  crossvpc-eni-primary-cleanup-delay: "30s"
```

### 8.2 Node标签配置

```yaml
# 节点标签配置CrossVPC支持
apiVersion: v1
kind: Node
metadata:
  name: worker-node-1
  labels:
    # 现有标签
    cce.baidubce.com/eni-use-mode: "Secondary"

    # CrossVPC支持标签
    cce.baidubce.com/crossvpc-eni-enabled: "true"
    cce.baidubce.com/crossvpc-eni-max-primary: "2"    # 最大独占ENI数
    cce.baidubce.com/crossvpc-eni-max-secondary: "8"  # 最大共享ENI数
```

## 9. 故障排查指南

### 9.1 常见问题诊断

```bash
# 1. 检查CrossVPC ENI状态
kubectl get enis -l cce.baidubce.com/eni-type=crossvpc

# 2. 检查特定模式的ENI
kubectl get enis -l cce.baidubce.com/eni-mode=primary
kubectl get enis -l cce.baidubce.com/eni-mode=secondary

# 3. 检查Pod的CrossVPC配置
kubectl get pod <pod-name> -o yaml | grep cross-vpc-eni

# 4. 检查ENI详细信息
kubectl describe eni <crossvpc-eni-name>

# 5. 检查NetResourceSet中的CrossVPC池
kubectl get nrs <node-name> -o yaml | grep -A 20 crossVPCENI
```

### 9.2 日志分析

```go
// 关键日志记录点
func logCrossVPCOperation(operation, mode, eniName string, config *CrossVPCConfig) {
    log.WithFields(logrus.Fields{
        "operation":  operation,
        "mode":       mode,
        "eni":        eniName,
        "userID":     config.UserID,
        "vpcCIDR":    config.VPCCIDR,
        "subnetID":   config.SubnetID,
    }).Info("CrossVPC ENI operation")
}

// 错误日志记录
func logCrossVPCError(operation, mode string, err error, config *CrossVPCConfig) {
    log.WithFields(logrus.Fields{
        "operation": operation,
        "mode":      mode,
        "error":     err.Error(),
        "userID":    config.UserID,
        "vpcCIDR":   config.VPCCIDR,
    }).Error("CrossVPC ENI operation failed")
}
```

## 10. 总结

这个双模式CrossVPCEni方案具有以下核心优势：

1. **最大化复用现有逻辑**：
   - 独占模式复用90%的Primary模式逻辑
   - 共享模式复用90%的Secondary模式逻辑
   - 统一使用现有的ENI CRD和管理框架

2. **灵活的使用方式**：
   - 用户可根据需求选择独占或共享模式
   - 支持混合部署，同一节点可同时运行两种模式
   - 提供成本和性能的平衡选择

3. **平滑的实施路径**：
   - 可以分阶段实施，先支持一种模式
   - 完全向后兼容，不影响现有功能
   - 使用相同的运维工具和流程

4. **统一的管理体验**：
   - 使用相同的kubectl命令管理
   - 统一的监控和告警体系
   - 一致的故障排查流程

通过这种模式映射和分发机制，真正实现了"尽可能复用当前逻辑"的目标，为用户提供了既经济又高性能的CrossVPCEni解决方案。
