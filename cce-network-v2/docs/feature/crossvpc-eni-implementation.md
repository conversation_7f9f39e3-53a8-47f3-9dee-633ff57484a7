# CrossVPCEni 特性改造实施文档

## 1. 概述

本文档描述了在当前CCE CNI驱动代码库中实现CrossVPCEni（跨VPC弹性网络接口）特性的详细改造方案。该方案基于现有的vpc-eni架构，通过扩展和复用现有组件来实现跨VPC网络连接功能。

## 2. 改造目标

- **功能目标**：实现Pod使用来自不同VPC的ENI，支持跨VPC网络通信
- **架构目标**：最大化复用现有框架，保持架构一致性
- **性能目标**：确保不影响现有vpc-eni模式的性能
- **兼容目标**：向后兼容，支持渐进式部署

## 3. 核心改造策略

### 3.1 复用现有组件

| 现有组件 | 复用方式 | 扩展内容 |
|---------|---------|---------|
| `pkg/ipam/ipam.go` | 扩展IPAM分配器 | 新增CrossVPCEni分配器类型 |
| `pkg/enim/enim.go` | 基础ENI管理框架 | 扩展支持跨VPC ENI管理 |
| `plugins/enim/` | CNI插件框架 | 新增crossvpc-eni插件 |
| `pkg/bce/bcesync/eni.go` | ENI状态同步 | 扩展支持CrossVPCEni状态 |
| NetResourceSet CRD | 节点资源管理 | 扩展支持跨VPC ENI信息 |

### 3.2 新增组件

- **CrossVPCEni CRD**：跨VPC ENI资源定义
- **CrossVPCEni IPAM**：专用的跨VPC ENI分配器
- **crossvpc-eni CNI插件**：跨VPC网络配置插件

## 4. 详细改造方案

### 4.1 阶段一：CRD定义和基础框架

#### 4.1.1 新增CrossVPCEni CRD定义

**文件位置**：`cce-network-v2/pkg/k8s/apis/cce.baidubce.com/v2/crossvpc_eni_types.go`

```go
// CrossVPCEni 跨VPC ENI资源定义
type CrossVPCEni struct {
    metav1.TypeMeta   `json:",inline"`
    metav1.ObjectMeta `json:"metadata,omitempty"`
    Spec   CrossVPCEniSpec   `json:"spec,omitempty"`
    Status CrossVPCEniStatus `json:"status,omitempty"`
}

type CrossVPCEniSpec struct {
    UserID                           string   `json:"userID"`
    SubnetID                         string   `json:"subnetID"`
    SecurityGroupIDs                 []string `json:"securityGroupIDs"`
    VPCCIDR                          string   `json:"vpcCIDR"`
    PrivateIPAddress                 string   `json:"privateIPAddress,omitempty"`
    BoundInstanceID                  string   `json:"boundInstanceID"`
    DefaultRouteInterfaceDelegation  string   `json:"defaultRouteInterfaceDelegation,omitempty"`
    DefaultRouteExcludedCidrs        []string `json:"defaultRouteExcludedCidrs,omitempty"`
}

type CrossVPCEniStatus struct {
    EniID               string                    `json:"eniID,omitempty"`
    EniStatus           EniStatus                 `json:"eniStatus"`
    PrimaryIPAddress    string                    `json:"primaryIPAddress,omitempty"`
    MacAddress          string                    `json:"macAddress,omitempty"`
    VPCID               string                    `json:"vpcID,omitempty"`
    InvolvedContainerID string                    `json:"involvedContainerID,omitempty"`
    Conditions          []CrossVPCEniCondition    `json:"conditions,omitempty"`
}
```

#### 4.1.2 扩展CRD注册机制

**文件位置**：`cce-network-v2/pkg/k8s/apis/cce.baidubce.com/client/register.go`

```go
// 在现有CRD注册中添加CrossVPCEni
resourceToCreateFnMapping := map[string]crdCreationFn{
    synced.CRDResourceName(k8sconstv2.NRSName):        createNetResourceSetCRD,
    synced.CRDResourceName(k8sconstv2.CEPName):        createCEPCRD,
    synced.CRDResourceName(k8sconstv2.CrossVPCEniName): createCrossVPCEniCRD, // 新增
}
```

### 4.2 阶段二：IPAM集成

#### 4.2.1 扩展IPAM分配器

**文件位置**：`cce-network-v2/pkg/ipam/ipam.go`

```go
// 在现有IPAM初始化中添加CrossVPCEni支持
case ipamOption.IPAMCrossVPCEni:
    log.Info("Initializing CrossVPCEni IPAM")
    if c.IPv4Enabled() {
        ipam.IPv4Allocator = newCrossVPCEniAllocator(IPv4, c, owner, k8sEventReg)
    }
    if c.IPv6Enabled() {
        ipam.IPv6Allocator = newCrossVPCEniAllocator(IPv6, c, owner, k8sEventReg)
    }
```

#### 4.2.2 实现CrossVPCEni分配器

**文件位置**：`cce-network-v2/pkg/ipam/crossvpc_eni_allocator.go`

```go
type crossVPCEniAllocator struct {
    // 基于现有CRD分配器架构
    *crdAllocator
    crossVPCEniClient crossvpceni.Interface
}

func (c *crossVPCEniAllocator) Allocate(ctx context.Context, owner string) (*AllocationResult, error) {
    // 实现跨VPC ENI分配逻辑
    // 复用现有的分配框架，扩展跨VPC特性
}
```

### 4.3 阶段三：ENI管理器扩展

#### 4.3.1 扩展ENI管理器

**文件位置**：`cce-network-v2/pkg/enim/enim.go`

```go
// 扩展现有ENI管理器支持CrossVPCEni
func NewCrossVPCEniManager(c ipam.Configuration, watcher *watchers.K8sWatcher) ENIManagerServer {
    return &crossVPCEniManager{
        baseManager: NewENIManager(c, watcher), // 复用现有基础管理器
        crossVPCEniClient: watcher.GetCrossVPCEniClient(),
    }
}

type crossVPCEniManager struct {
    baseManager       ENIManagerServer
    crossVPCEniClient crossvpceni.Interface
}
```

### 4.4 阶段四：CNI插件实现

#### 4.4.1 新增crossvpc-eni插件

**文件位置**：`cce-network-v2/plugins/crossvpc-eni/main.go`

```go
// 基于现有enim插件架构实现
func cmdAdd(args *skel.CmdArgs) error {
    // 1. 解析配置和Pod注解
    cfg, err := loadConf(args.StdinData)
    if err != nil {
        return err
    }
    
    // 2. 调用CrossVPCEni IPAM分配ENI
    ipam, releaseFunc, err := allocateCrossVPCEniWithAgent(cfg, args)
    if err != nil {
        return err
    }
    defer releaseFunc()
    
    // 3. 配置跨VPC网络接口（复用现有网络配置逻辑）
    return configureCrossVPCNetwork(args, ipam)
}
```

## 5. 改造后的系统架构

### 5.1 组件交互时序图

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant CNI as CrossVPC-ENI Plugin
    participant IPAM as CrossVPCEni IPAM
    participant CRD as CrossVPCEni CRD
    participant Cloud as Cloud Controller
    participant Node as Node
    participant NRS as NetResourceSet

    Note over Pod,NRS: 【关键步骤1】Pod创建触发CNI ADD
    Pod->>CNI: 创建Pod，触发CNI ADD
    CNI->>CNI: 解析Pod注解获取跨VPC配置
    
    Note over CNI,IPAM: 【关键步骤2】复用现有IPAM框架
    CNI->>IPAM: AllocateIP请求(扩展现有接口)
    IPAM->>IPAM: 检查Pod跨VPC注解
    IPAM->>IPAM: 验证节点ENI状态(复用现有逻辑)
    
    Note over IPAM,CRD: 【关键步骤3】创建CrossVPCEni CRD
    IPAM->>CRD: 创建CrossVPCEni资源
    CRD-->>IPAM: 返回CRD对象
    IPAM->>IPAM: 设置状态为Pending
    IPAM-->>CNI: 返回ENI信息
    
    Note over Cloud,Node: 【关键步骤4】异步ENI创建(扩展现有状态机)
    Cloud->>Cloud: 创建跨VPC ENI实例
    Cloud->>Node: 附加ENI到实例
    Cloud->>CRD: 更新状态为Inuse
    Cloud->>NRS: 同步ENI信息到NRS(扩展现有逻辑)
    
    Note over CNI,Pod: 【关键步骤5】网络配置(基于现有框架)
    CNI->>CNI: 等待ENI状态变为Inuse(复用等待逻辑)
    CNI->>Node: 配置跨VPC网络接口
    CNI->>Node: 设置跨VPC路由规则
    CNI->>Pod: 返回网络配置
```

### 5.2 状态管理流程

```mermaid
stateDiagram-v2
    [*] --> Pending: 创建CrossVPCEni CRD
    Pending --> Created: 云平台创建ENI(复用现有状态机)
    Created --> Attaching: 开始附加到实例
    Attaching --> Inuse: 附加完成
    Inuse --> Detaching: 开始分离
    Detaching --> Detached: 分离完成
    Detached --> Deleted: 删除ENI
    Deleted --> [*]
    
    Pending --> Deleted: 创建失败
    Created --> Deleted: 创建后直接删除
    Attaching --> Deleted: 附加失败
    Detaching --> Deleted: 分离失败
```

## 6. 实施计划

### 6.1 开发阶段

| 阶段 | 工作内容 | 预估工期 | 依赖关系 |
|------|---------|---------|---------|
| 阶段1 | CRD定义和基础框架 | 1周 | 无 |
| 阶段2 | IPAM集成 | 2周 | 阶段1完成 |
| 阶段3 | ENI管理器扩展 | 1.5周 | 阶段1,2完成 |
| 阶段4 | CNI插件实现 | 2周 | 阶段1,2,3完成 |
| 阶段5 | 状态同步和监控 | 1周 | 阶段1-4完成 |
| 阶段6 | 测试和优化 | 1.5周 | 所有阶段完成 |

### 6.2 测试策略

1. **单元测试**：每个新增组件都需要完整的单元测试覆盖
2. **集成测试**：验证与现有组件的集成效果
3. **端到端测试**：完整的Pod创建到网络配置流程测试
4. **性能测试**：确保不影响现有vpc-eni模式性能
5. **兼容性测试**：验证向后兼容性

## 7. 风险控制

### 7.1 技术风险

- **架构兼容性**：通过扩展而非修改现有代码来降低风险
- **性能影响**：独立的分配器避免影响现有功能
- **状态一致性**：复用现有的状态同步机制

### 7.2 实施风险

- **渐进式部署**：支持功能开关，可以逐步启用
- **回滚机制**：保留现有功能不变，新功能可以快速禁用
- **监控告警**：完善的监控和日志记录

## 8. 后续扩展

1. **多VPC支持**：支持Pod连接多个不同VPC的ENI
2. **网络策略**：集成Kubernetes网络策略支持
3. **服务网格**：与服务网格组件集成
4. **IPv6支持**：完整的IPv6跨VPC支持

## 9. 关键技术实现细节

### 9.1 Pod注解解析

**支持的Pod注解**：
```yaml
annotations:
  cross-vpc-eni.cce.io/userID: "user123"
  cross-vpc-eni.cce.io/subnetID: "sbn-abc123"
  cross-vpc-eni.cce.io/securityGroupIDs: "sg-123,sg-456"
  cross-vpc-eni.cce.io/vpcCidr: "***********/16"
  cross-vpc-eni.cce.io/privateIPAddress: "*************"
  cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation: "eni"
  cross-vpc-eni.cce.io/defaultRouteExcludedCidrs: "10.0.0.0/8,**********/12"
```

### 9.2 网络配置实现

**路由配置策略**：
1. **默认路由委托**：支持将默认路由委托给跨VPC ENI
2. **路由排除**：支持排除特定CIDR不走跨VPC路由
3. **源路由**：基于源IP的路由策略

**网络接口配置**：
```go
// 基于现有的网络配置框架扩展
func configureCrossVPCNetwork(args *skel.CmdArgs, ipam *models.IPAMResponse) error {
    // 1. 获取ENI网络接口（复用现有逻辑）
    link, err := getCrossVPCEniLink(ipam.Address.IP)
    if err != nil {
        return err
    }

    // 2. 移动接口到容器命名空间（复用现有逻辑）
    err = moveLinkToNetns(link, args.Netns, args.IfName)
    if err != nil {
        return err
    }

    // 3. 配置跨VPC路由（新增逻辑）
    return configureCrossVPCRoutes(args, ipam)
}
```

### 9.3 状态同步机制

**扩展现有ENI状态同步**：
```go
// 在现有eni.go中扩展CrossVPCEni状态同步
func (esm *eniStateMachine) syncCrossVPCEniStatus() error {
    // 复用现有的状态同步框架
    // 扩展支持CrossVPCEni特有的状态字段
    return esm.updateCrossVPCEniResource()
}
```

### 9.4 垃圾回收机制

**扩展现有GC逻辑**：
```go
// 在IPAM中扩展垃圾回收
func (ipam *crossVPCEniIPAM) gc() {
    // 复用现有的GC框架
    // 清理泄漏的CrossVPCEni资源
    ipam.gcLeakedCrossVPCEnis()
}
```

## 10. 配置示例

### 10.1 CNI配置

```json
{
  "cniVersion": "0.4.0",
  "name": "crossvpc-eni",
  "type": "crossvpc-eni",
  "ifName": "eth0",
  "endpoint": "unix:///var/run/cce-ipam.sock",
  "ipam": {
    "type": "crossvpc-eni-ipam"
  }
}
```

### 10.2 Pod配置示例

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: crossvpc-pod
  annotations:
    cross-vpc-eni.cce.io/userID: "user123"
    cross-vpc-eni.cce.io/subnetID: "sbn-abc123"
    cross-vpc-eni.cce.io/securityGroupIDs: "sg-123,sg-456"
    cross-vpc-eni.cce.io/vpcCidr: "***********/16"
    cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation: "eni"
spec:
  containers:
  - name: app
    image: nginx
```

## 11. 监控和故障排查

### 11.1 关键指标

- **CrossVPCEni分配成功率**：监控跨VPC ENI分配的成功率
- **CrossVPCEni分配延迟**：监控分配耗时
- **跨VPC连通性**：监控跨VPC网络连通性
- **资源使用情况**：监控CrossVPCEni资源使用情况

### 11.2 日志记录

**关键日志点**：
```go
// IPAM分配日志
log.WithFields(logrus.Fields{
    "pod": podName,
    "crossvpc-eni": eniID,
    "vpc": vpcID,
    "subnet": subnetID,
}).Info("CrossVPCEni allocated successfully")

// 网络配置日志
log.WithFields(logrus.Fields{
    "interface": ifName,
    "ip": ipAddress,
    "routes": routes,
}).Info("CrossVPC network configured")
```

### 11.3 故障排查指南

**常见问题及解决方案**：

1. **ENI分配失败**
   - 检查跨VPC配额限制
   - 验证安全组配置
   - 确认子网可用IP数量

2. **网络连通性问题**
   - 检查路由配置
   - 验证安全组规则
   - 确认VPC对等连接状态

3. **性能问题**
   - 监控ENI分配延迟
   - 检查并发分配数量
   - 优化资源预分配策略

## 12. 总结

本改造方案通过最大化复用现有架构和组件，以扩展的方式实现CrossVPCEni特性，既保证了功能的完整性，又确保了系统的稳定性和可维护性。该方案具有以下优势：

- **低风险**：基于成熟的现有架构
- **高复用**：充分利用现有代码和框架
- **易维护**：保持架构一致性
- **可扩展**：为未来功能扩展预留空间

通过这种渐进式的改造方式，可以在保证系统稳定性的前提下，快速实现CrossVPCEni特性，为用户提供灵活的跨VPC网络解决方案。
