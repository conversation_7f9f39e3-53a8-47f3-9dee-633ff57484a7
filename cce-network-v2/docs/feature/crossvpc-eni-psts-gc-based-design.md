# CrossVPCEni 基于PSTS和GC机制的实现方案

## 1. 设计理念

### 1.1 参考现有机制
- **共享模式**：参考非固定IP模式的PSTS分配机制
- **ENI延迟删除**：参考固定IP模式的CEP GC机制
- **复用现有框架**：最大化利用现有的IPAM和GC基础设施

### 1.2 核心优势
- **实现简化**：复用成熟的PSTS分配和GC逻辑
- **稳定可靠**：基于已验证的生产环境代码
- **维护成本低**：减少新增代码量，降低维护复杂度

## 2. 共享模式：基于PSTS分配

### 2.1 PSTS分配机制复用

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant Agent as Agent
    participant CEP as CCEEndpoint
    participant Operator as CrossVPC Operator
    participant Cloud as Cloud API
    participant LocalPool as Local IP Pool

    Note over Pod,LocalPool: 【共享模式 - 参考PSTS分配】
    
    Pod->>Agent: 创建Pod (mode=secondary)
    Agent->>CEP: 创建CEP (type=CrossVPCSecondary)
    
    Operator->>CEP: 检测CEP创建
    Operator->>LocalPool: 尝试从本地池分配IP
    
    alt 本地池有可用IP
        LocalPool-->>Operator: 返回IP地址
        Operator->>CEP: 更新IP信息
    else 本地池无可用IP
        Operator->>Cloud: 向子网申请新IP
        Note right of Cloud: AllocatePrivateIP(subnetID, userID)
        Cloud-->>Operator: 返回IP信息
        Operator->>LocalPool: 将IP加入本地池
        Operator->>CEP: 更新IP信息
    end
    
    Agent->>CEP: 检测到Ready状态
    Agent->>Pod: 配置网络(辅助IP模式)
```

### 2.2 基于PSTS的实现

```go
// pkg/endpoint/operator_crossvpc_psts_provider.go
type crossVPCPSTSProvider struct {
    *EndpointManager
    localPool *LocalIPPool  // 复用PSTS的本地IP池机制
}

// 参考pstsAllocatorProvider.AllocateIP的实现
func (provider *crossVPCPSTSProvider) AllocateIP(ctx context.Context, log *logrus.Entry, resource *ccev2.CCEEndpoint) error {
    var (
        owner          = resource.Namespace + "/" + resource.Name
        status         = &resource.Status
        releaseIPFuncs []func()
        err            error
        
        action = &DirectIPAction{
            NodeName: resource.Spec.Network.IPAllocation.NodeName,
            Owner:    owner,
        }
    )
    
    config := resource.Spec.Network.IPAllocation.CrossVPCConfig
    log = log.WithField("crossvpc", config.UserID).WithField("endpoint", owner)
    log.Info("start allocate CrossVPC secondary IP")
    
    operation, err := provider.directIPAllocator.NodeEndpoint(resource)
    if err != nil {
        log.Errorf("failed to get node endpoint %v", err)
        return err
    }
    
    if len(status.Networking.Addressing) == 0 {
        // 参考PSTS的本地池分配逻辑
        localAllocator := &crossVPCLocalAllocator{
            localPool: provider.localPool,
            config:    config,
            log:       log.WithField("mod", "crossVPCLocalAllocator"),
        }
        
        ipv4Address, release := localAllocator.allocateNext()
        if ipv4Address != nil {
            // 从本地池分配成功
            action.Addressing = []*ccev2.AddressPair{ipv4Address}
            releaseIPFuncs = append(releaseIPFuncs, release)
        } else {
            // 本地池无可用IP，向云平台申请
            log.Info("no available IP in local pool, allocating from cloud")
            // 这里会调用云平台API分配新IP，并加入本地池
        }
    } else {
        // 复用现有IP
        action.Addressing = status.Networking.Addressing
        action.SubnetID = status.Networking.Addressing[0].Subnet
    }
    
    defer func() {
        if err != nil && len(releaseIPFuncs) > 0 {
            for _, release := range releaseIPFuncs {
                release()
            }
        }
    }()
    
    log = log.WithField("step", "allocate crossvpc secondary ip").
        WithField("action", logfields.Repr(action))
    
    err = operation.AllocateIP(ctx, action)
    if err != nil {
        log.WithError(err).Errorf("failed to allocate crossvpc secondary ip")
        return err
    }
    
    log.Info("allocate crossvpc secondary ip success")
    
    status.Networking.Addressing = action.Addressing
    status.NodeSelectorRequirement = action.NodeSelectorRequirement
    return nil
}

// CrossVPC本地分配器
type crossVPCLocalAllocator struct {
    localPool *LocalIPPool
    config    *ccev2.CrossVPCAllocationConfig
    log       *logrus.Entry
}

func (local *crossVPCLocalAllocator) allocateNext() (*ccev2.AddressPair, func()) {
    // 构造池Key
    poolKey := fmt.Sprintf("crossvpc-%s-%s", local.config.UserID, local.config.SubnetID)
    
    // 从本地池获取可用IP
    allocator := local.localPool.GetAllocator(poolKey)
    if allocator == nil {
        return nil, nil
    }
    
    // 分配IP
    result, err := allocator.AllocateNext("crossvpc-secondary")
    if err != nil {
        local.log.WithError(err).Debug("allocate from local pool failed")
        return nil, nil
    }
    
    local.log.WithField("ip", result.IP.String()).Debug("allocate from local pool success")
    
    return &ccev2.AddressPair{
        IP:     result.IP.String(),
        Family: ccev2.IPv4Family,
        Subnet: local.config.SubnetID,
    }, func() {
        allocator.ReleaseIP(result.IP)
    }
}
```

## 3. ENI延迟删除：基于CEP GC机制

### 3.1 GC机制复用

```mermaid
flowchart TD
    A[GC定时器启动] --> B[扫描所有CrossVPC ENI]
    B --> C{ENI有辅助IP?}
    
    C -->|有| D[跳过删除]
    C -->|无| E[检查过期时间]
    
    E --> F{超过延迟时间?}
    F -->|否| G[加入过期映射]
    F -->|是| H[执行ENI删除]
    
    G --> I[等待下次GC]
    H --> J[从过期映射移除]
    
    I --> B
    J --> K[删除完成]
    
    style E fill:#e3f2fd
    style H fill:#fff3e0
    style K fill:#e8f5e8
```

### 3.2 基于CEP GC的实现

```go
// pkg/endpoint/operator_crossvpc_gc.go
type crossVPCGCer struct {
    *EndpointManager
    expiredENIMap map[string]*eniExpireTime  // 参考agentGCer.expiredIPMap
    log           *logrus.Entry
}

type eniExpireTime struct {
    eniID       string
    userID      string
    subnetID    string
    time        time.Time
    lastVisited time.Time
}

// 参考EndpointManager.gcReusedEndpointTTL的实现
func (gcer *crossVPCGCer) gcCrossVPCENIs() {
    var (
        logEntry = gcer.log.WithFields(logrus.Fields{
            logfields.LogSubsys: "CrossVPCGC",
            "event":             "gcCrossVPCENI",
        })
        enisToDelete []string
    )
    
    // 扫描所有CrossVPC类型的ENI
    enis, err := gcer.listCrossVPCENIs()
    if err != nil {
        logEntry.WithError(err).Error("failed to list CrossVPC ENIs")
        return
    }
    
    now := time.Now()
    for _, eni := range enis {
        // 跳过独占模式的ENI（由ENI状态机处理）
        if eni.Mode == "primary" {
            continue
        }
        
        log := logEntry.WithFields(logrus.Fields{
            "eniID":    eni.ID,
            "userID":   eni.UserID,
            "subnetID": eni.SubnetID,
        })
        
        // 检查ENI是否还有辅助IP
        if gcer.hasSecondaryIPs(eni.ID) {
            // 还有辅助IP，从过期映射中移除
            delete(gcer.expiredENIMap, eni.ID)
            continue
        }
        
        // 检查是否已在过期映射中
        expireTime, exists := gcer.expiredENIMap[eni.ID]
        if !exists {
            // 首次发现无辅助IP，加入过期映射
            gcer.expiredENIMap[eni.ID] = &eniExpireTime{
                eniID:       eni.ID,
                userID:      eni.UserID,
                subnetID:    eni.SubnetID,
                time:        now,
                lastVisited: now,
            }
            log.Info("ENI added to expiration map")
            continue
        }
        
        // 更新最后访问时间
        expireTime.lastVisited = now
        
        // 检查是否超过延迟时间
        delayDuration := gcer.getSharedENIDeletionDelay()
        if now.After(expireTime.time.Add(delayDuration)) {
            log.Infof("ENI expired after %s, preparing to delete", delayDuration.String())
            enisToDelete = append(enisToDelete, eni.ID)
        }
    }
    
    // 清理过期映射中不存在的ENI
    for eniID, expireTime := range gcer.expiredENIMap {
        if expireTime.lastVisited != now {
            delete(gcer.expiredENIMap, eniID)
        }
    }
    
    // 执行ENI删除
    for _, eniID := range enisToDelete {
        err := gcer.deleteCrossVPCENI(eniID)
        if err != nil {
            logEntry.WithError(err).WithField("eniID", eniID).Error("failed to delete CrossVPC ENI")
        } else {
            logEntry.WithField("eniID", eniID).Info("CrossVPC ENI deleted successfully")
            delete(gcer.expiredENIMap, eniID)
        }
    }
}

// 参考agentGCer.dynamicUsedIPsGC的检查逻辑
func (gcer *crossVPCGCer) hasSecondaryIPs(eniID string) bool {
    // 查询ENI的辅助IP数量
    eni, err := gcer.cloudAPI.GetENI(context.Background(), eniID)
    if err != nil {
        gcer.log.WithError(err).WithField("eniID", eniID).Error("failed to get ENI info")
        return true // 出错时保守处理，不删除
    }
    
    return len(eni.SecondaryIPs) > 0
}

func (gcer *crossVPCGCer) getSharedENIDeletionDelay() time.Duration {
    // 参考固定IP的TTL配置
    return operatorOption.Config.CrossVPCENISharedDeletionDelay
}

func (gcer *crossVPCGCer) deleteCrossVPCENI(eniID string) error {
    // 执行ENI删除
    return gcer.cloudAPI.DeleteENI(context.Background(), eniID)
}
```

### 3.3 GC集成到现有框架

```go
// pkg/endpoint/operator_endpoint_manager.go
func (manager *EndpointManager) Resync(ctx context.Context) error {
    manager.gcReusedEndpointTTL()
    
    // 新增CrossVPC ENI GC
    if manager.crossVPCGCer != nil {
        manager.crossVPCGCer.gcCrossVPCENIs()
    }
    
    return nil
}

// 在EndpointManager初始化时创建CrossVPC GCer
func NewEndpointManager(...) *EndpointManager {
    manager := &EndpointManager{
        // 现有字段...
    }
    
    // 初始化CrossVPC GCer
    if operatorOption.Config.CrossVPCENIEnabled {
        manager.crossVPCGCer = &crossVPCGCer{
            EndpointManager: manager,
            expiredENIMap:   make(map[string]*eniExpireTime),
            log:            managerLog.WithField("component", "crossvpc-gc"),
        }
    }
    
    return manager
}
```

## 4. 配置集成

### 4.1 配置扩展

```go
// pkg/option/config.go
type DaemonConfig struct {
    // 现有配置...
    
    // CrossVPC ENI配置
    CrossVPCENIEnabled              bool          `mapstructure:"crossvpc-eni-enabled"`
    CrossVPCENISharedDeletionDelay  time.Duration `mapstructure:"crossvpc-eni-shared-deletion-delay"`
    CrossVPCENIGCInterval           time.Duration `mapstructure:"crossvpc-eni-gc-interval"`
}

// 默认配置
var (
    DefaultCrossVPCENISharedDeletionDelay = 5 * time.Minute
    DefaultCrossVPCENIGCInterval          = 2 * time.Minute
)
```

### 4.2 ConfigMap配置

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-network-v2-config
data:
  # CrossVPC ENI配置
  crossvpc-eni-enabled: "true"
  crossvpc-eni-shared-deletion-delay: "300s"    # 共享ENI延迟删除时间
  crossvpc-eni-gc-interval: "120s"              # GC检查间隔
```

## 5. 优势总结

### 5.1 实现简化
- **复用PSTS分配**：共享模式直接复用成熟的本地池分配逻辑
- **复用CEP GC**：ENI延迟删除复用固定IP的TTL GC机制
- **最小化新增代码**：主要是适配层代码，核心逻辑复用

### 5.2 稳定可靠
- **生产验证**：基于已在生产环境验证的PSTS和GC代码
- **成熟机制**：复用经过长期优化的分配和回收逻辑
- **错误处理**：继承现有机制的完善错误处理

### 5.3 维护友好
- **统一框架**：与现有IPAM和GC框架保持一致
- **配置统一**：复用现有的配置管理机制
- **监控集成**：可以复用现有的监控和告警体系

### 5.4 性能优化
- **本地池缓存**：共享模式利用本地IP池提高分配性能
- **批量GC**：延迟删除机制减少频繁的云平台API调用
- **智能调度**：基于现有GC的智能调度机制

这个基于PSTS和GC机制的实现方案，通过最大化复用现有的成熟代码，实现了CrossVPC ENI的高效、可靠管理，同时大大降低了实现复杂度和维护成本。
