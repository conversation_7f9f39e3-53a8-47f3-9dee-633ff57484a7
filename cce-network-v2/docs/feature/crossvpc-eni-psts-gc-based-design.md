# CrossVPCEni 基于PSTS和GC机制的实现方案

## 1. 设计理念

### 1.1 参考现有机制
- **共享模式**：参考非固定IP模式的PSTS分配机制
- **ENI延迟删除**：参考固定IP模式的CEP GC机制
- **复用现有框架**：最大化利用现有的IPAM和GC基础设施

### 1.2 核心优势
- **实现简化**：复用成熟的PSTS分配和GC逻辑
- **稳定可靠**：基于已验证的生产环境代码
- **维护成本低**：减少新增代码量，降低维护复杂度

## 2. 共享模式：基于非reuse IP的PSTS分配

### 2.1 非reuse IP模式的PSTS分配机制

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant Agent as Agent
    participant CEP as CCEEndpoint
    participant Operator as CrossVPC Operator
    participant Cloud as Cloud API

    Note over Pod,Cloud: 【共享模式 - 参考非reuse IP的PSTS分配】

    Pod->>Agent: 创建Pod (mode=secondary)
    Agent->>CEP: 创建CEP (type=CrossVPCSecondary)

    Operator->>CEP: 检测CEP创建
    Note over Operator: 跳过本地池分配<br/>(非reuse IP模式)

    Operator->>Cloud: 直接向子网申请新IP
    Note right of Cloud: AllocatePrivateIP(subnetID, userID)
    Cloud-->>Operator: 返回IP信息
    Operator->>CEP: 更新IP信息

    Agent->>CEP: 检测到Ready状态
    Agent->>Pod: 配置网络(辅助IP模式)
```

### 2.2 基于非reuse IP的PSTS实现

```go
// pkg/endpoint/operator_crossvpc_psts_provider.go
type crossVPCPSTSProvider struct {
    *EndpointManager
}

// 参考非reuse IP模式的pstsAllocatorProvider.AllocateIP实现
func (provider *crossVPCPSTSProvider) AllocateIP(ctx context.Context, log *logrus.Entry, resource *ccev2.CCEEndpoint) error {
    var (
        owner  = resource.Namespace + "/" + resource.Name
        status = &resource.Status
        err    error

        action = &DirectIPAction{
            NodeName: resource.Spec.Network.IPAllocation.NodeName,
            Owner:    owner,
        }
    )

    config := resource.Spec.Network.IPAllocation.CrossVPCConfig
    log = log.WithField("crossvpc", config.UserID).WithField("endpoint", owner)
    log.Info("start allocate CrossVPC secondary IP")

    operation, err := provider.directIPAllocator.NodeEndpoint(resource)
    if err != nil {
        log.Errorf("failed to get node endpoint %v", err)
        return err
    }

    if len(status.Networking.Addressing) == 0 {
        // 非reuse IP模式：直接分配新IP，不使用本地池
        // 设置子网ID，让DirectEndpointOperation直接分配
        action.SubnetID = config.SubnetID
        action.CrossVPCConfig = &DirectIPCrossVPCConfig{
            UserID:           config.UserID,
            VPCCIDR:          config.VPCCIDR,
            SecurityGroupIDs: config.SecurityGroupIDs,
            Mode:             "secondary",
        }

        log.Info("allocating new CrossVPC secondary IP from cloud")
    } else {
        // 复用现有IP
        action.Addressing = status.Networking.Addressing
        action.SubnetID = status.Networking.Addressing[0].Subnet
    }

    log = log.WithField("step", "allocate crossvpc secondary ip").
        WithField("action", logfields.Repr(action))

    err = operation.AllocateIP(ctx, action)
    if err != nil {
        log.WithError(err).Errorf("failed to allocate crossvpc secondary ip")
        return err
    }

    log.Info("allocate crossvpc secondary ip success")

    status.Networking.Addressing = action.Addressing
    status.NodeSelectorRequirement = action.NodeSelectorRequirement
    return nil
}

// DirectEndpointOperation扩展，支持CrossVPC配置
type DirectIPCrossVPCConfig struct {
    UserID           string   `json:"userID"`
    VPCCIDR          string   `json:"vpcCIDR"`
    SecurityGroupIDs []string `json:"securityGroupIDs"`
    Mode             string   `json:"mode"`  // "primary" | "secondary"
}

type DirectIPAction struct {
    // 现有字段...
    NodeName                    string
    Owner                       string
    SubnetID                    string
    Addressing                  []*ccev2.AddressPair
    NodeSelectorRequirement     []ccev2.NodeSelectorRequirement

    // 新增CrossVPC配置
    CrossVPCConfig              *DirectIPCrossVPCConfig `json:"crossVPCConfig,omitempty"`
}
```

## 3. ENI延迟删除：基于CEP GC机制（修正版）

### 3.1 修正的GC机制

```mermaid
flowchart TD
    A[GC定时器启动] --> B[扫描所有CrossVPC ENI CR]
    B --> C{ENI CR有辅助IP?}

    C -->|有| D[检查CEP使用情况]
    C -->|无| E[检查过期时间]

    D --> F{有CEP使用该ENI?}
    F -->|有| G[从过期映射移除]
    F -->|无| E

    E --> H{超过延迟时间?}
    H -->|否| I[加入/更新过期映射]
    H -->|是| J[标记ENI CR删除]

    I --> K[等待下次GC]
    J --> L[ENI状态机处理删除]
    G --> K

    K --> B
    L --> M[删除完成]

    style D fill:#fff3e0
    style F fill:#e3f2fd
    style J fill:#ffcdd2
    style L fill:#e8f5e8
```

### 3.2 修正的CEP GC实现

```go
// pkg/endpoint/operator_crossvpc_gc.go
type crossVPCGCer struct {
    *EndpointManager
    expiredENIMap map[string]*eniExpireTime  // 参考agentGCer.expiredIPMap
    log           *logrus.Entry
}

type eniExpireTime struct {
    eniCRName   string
    userID      string
    subnetID    string
    time        time.Time
    lastVisited time.Time
}

// 参考EndpointManager.gcReusedEndpointTTL的实现，修正IP泄漏问题
func (gcer *crossVPCGCer) gcCrossVPCENIs() {
    var (
        logEntry = gcer.log.WithFields(logrus.Fields{
            logfields.LogSubsys: "CrossVPCGC",
            "event":             "gcCrossVPCENI",
        })
        eniCRsToDelete []string
    )

    // 扫描所有CrossVPC类型的ENI CR
    eniCRs, err := gcer.listCrossVPCENICRs()
    if err != nil {
        logEntry.WithError(err).Error("failed to list CrossVPC ENI CRs")
        return
    }

    now := time.Now()
    for _, eniCR := range eniCRs {
        // 跳过独占模式的ENI（由ENI状态机处理）
        if eniCR.Spec.UseMode == ccev2.ENIUseModeCrossVPCPrimary {
            continue
        }

        log := logEntry.WithFields(logrus.Fields{
            "eniCRName": eniCR.Name,
            "userID":    eniCR.Spec.CrossVPCConfig.UserID,
            "subnetID":  eniCR.Spec.ENI.SubnetID,
        })

        // 关键修正：检查是否有CEP正在使用该ENI
        if gcer.hasCEPsUsingENI(eniCR) {
            // 有CEP使用该ENI，从过期映射中移除
            delete(gcer.expiredENIMap, eniCR.Name)
            log.Debug("ENI CR has active CEPs, removing from expiration map")
            continue
        }

        // 检查ENI CR是否还有辅助IP（从CR状态读取，避免云平台API调用）
        if gcer.hasSecondaryIPsInCR(eniCR) {
            // 还有辅助IP，从过期映射中移除
            delete(gcer.expiredENIMap, eniCR.Name)
            log.Debug("ENI CR has secondary IPs, removing from expiration map")
            continue
        }

        // 检查是否已在过期映射中
        expireTime, exists := gcer.expiredENIMap[eniCR.Name]
        if !exists {
            // 首次发现无辅助IP且无CEP使用，加入过期映射
            gcer.expiredENIMap[eniCR.Name] = &eniExpireTime{
                eniCRName:   eniCR.Name,
                userID:      eniCR.Spec.CrossVPCConfig.UserID,
                subnetID:    eniCR.Spec.ENI.SubnetID,
                time:        now,
                lastVisited: now,
            }
            log.Info("ENI CR added to expiration map")
            continue
        }

        // 更新最后访问时间
        expireTime.lastVisited = now

        // 检查是否超过延迟时间
        delayDuration := gcer.getSharedENIDeletionDelay()
        if now.After(expireTime.time.Add(delayDuration)) {
            log.Infof("ENI CR expired after %s, preparing to delete", delayDuration.String())
            eniCRsToDelete = append(eniCRsToDelete, eniCR.Name)
        }
    }

    // 清理过期映射中不存在的ENI CR
    for eniCRName, expireTime := range gcer.expiredENIMap {
        if expireTime.lastVisited != now {
            delete(gcer.expiredENIMap, eniCRName)
        }
    }

    // 标记ENI CR删除，由ENI状态机处理实际删除
    for _, eniCRName := range eniCRsToDelete {
        err := gcer.markENICRForDeletion(eniCRName)
        if err != nil {
            logEntry.WithError(err).WithField("eniCRName", eniCRName).Error("failed to mark ENI CR for deletion")
        } else {
            logEntry.WithField("eniCRName", eniCRName).Info("ENI CR marked for deletion")
            delete(gcer.expiredENIMap, eniCRName)
        }
    }
}

// 关键修正：检查是否有CEP正在使用该ENI
func (gcer *crossVPCGCer) hasCEPsUsingENI(eniCR *ccev2.ENI) bool {
    // 查询所有CrossVPC类型的CEP
    ceps, err := gcer.k8sAPI.Lister().CCEEndpoints(metav1.NamespaceAll).List(labels.Everything())
    if err != nil {
        gcer.log.WithError(err).Error("failed to list CEPs")
        return true // 出错时保守处理，不删除
    }

    for _, cep := range ceps {
        // 检查是否为CrossVPC类型
        if cep.Spec.Network.IPAllocation == nil ||
           cep.Spec.Network.IPAllocation.Type != ccev2.IPAllocTypeCrossVPCSecondary {
            continue
        }

        // 检查是否使用该ENI
        if cep.Status.ENICRName == eniCR.Name {
            gcer.log.WithFields(logrus.Fields{
                "eniCRName": eniCR.Name,
                "cepName":   cep.Name,
                "namespace": cep.Namespace,
            }).Debug("found CEP using ENI CR")
            return true
        }

        // 检查addressing中是否包含该ENI的IP
        if cep.Status.Networking != nil {
            for _, addr := range cep.Status.Networking.Addressing {
                if addr.Interface == eniCR.Status.ENI.ID {
                    gcer.log.WithFields(logrus.Fields{
                        "eniCRName": eniCR.Name,
                        "eniID":     eniCR.Status.ENI.ID,
                        "cepName":   cep.Name,
                        "namespace": cep.Namespace,
                        "ip":        addr.IP,
                    }).Debug("found CEP using ENI IP")
                    return true
                }
            }
        }
    }

    return false
}

// 从ENI CR状态读取辅助IP信息，避免云平台API调用
func (gcer *crossVPCGCer) hasSecondaryIPsInCR(eniCR *ccev2.ENI) bool {
    return eniCR.Status.SecondaryIPCount > 0
}

func (gcer *crossVPCGCer) getSharedENIDeletionDelay() time.Duration {
    return operatorOption.Config.CrossVPCENISharedDeletionDelay
}

// 修正：通过ENI状态机删除，而不是直接调用云平台API
func (gcer *crossVPCGCer) markENICRForDeletion(eniCRName string) error {
    // 标记ENI CR删除，由ENI状态机处理实际的云平台删除操作
    return gcer.eniClient.ENIs().Delete(context.Background(), eniCRName, metav1.DeleteOptions{})
}

func (gcer *crossVPCGCer) listCrossVPCENICRs() ([]*ccev2.ENI, error) {
    // 列出所有CrossVPC类型的ENI CR
    return gcer.eniLister.List(labels.SelectorFromSet(labels.Set{
        "cce.baidubce.com/eni-type": "crossvpc",
    }))
}
```

### 3.3 GC集成到现有框架

```go
// pkg/endpoint/operator_endpoint_manager.go
func (manager *EndpointManager) Resync(ctx context.Context) error {
    manager.gcReusedEndpointTTL()
    
    // 新增CrossVPC ENI GC
    if manager.crossVPCGCer != nil {
        manager.crossVPCGCer.gcCrossVPCENIs()
    }
    
    return nil
}

// 在EndpointManager初始化时创建CrossVPC GCer
func NewEndpointManager(...) *EndpointManager {
    manager := &EndpointManager{
        // 现有字段...
    }
    
    // 初始化CrossVPC GCer
    if operatorOption.Config.CrossVPCENIEnabled {
        manager.crossVPCGCer = &crossVPCGCer{
            EndpointManager: manager,
            expiredENIMap:   make(map[string]*eniExpireTime),
            log:            managerLog.WithField("component", "crossvpc-gc"),
        }
    }
    
    return manager
}
```

## 4. 配置集成

### 4.1 配置扩展

```go
// pkg/option/config.go
type DaemonConfig struct {
    // 现有配置...
    
    // CrossVPC ENI配置
    CrossVPCENIEnabled              bool          `mapstructure:"crossvpc-eni-enabled"`
    CrossVPCENISharedDeletionDelay  time.Duration `mapstructure:"crossvpc-eni-shared-deletion-delay"`
    CrossVPCENIGCInterval           time.Duration `mapstructure:"crossvpc-eni-gc-interval"`
}

// 默认配置
var (
    DefaultCrossVPCENISharedDeletionDelay = 5 * time.Minute
    DefaultCrossVPCENIGCInterval          = 2 * time.Minute
)
```

### 4.2 ConfigMap配置

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-network-v2-config
data:
  # CrossVPC ENI配置
  crossvpc-eni-enabled: "true"
  crossvpc-eni-shared-deletion-delay: "300s"    # 共享ENI延迟删除时间
  crossvpc-eni-gc-interval: "120s"              # GC检查间隔
```

## 5. 优势总结

### 5.1 实现简化
- **复用非reuse IP的PSTS分配**：共享模式直接分配新IP，不使用本地池
- **复用CEP GC机制**：ENI延迟删除复用固定IP的TTL GC机制
- **ENI状态机删除**：通过标记删除让ENI状态机处理，不直接调用云平台API
- **最小化新增代码**：主要是适配层代码，核心逻辑复用

### 5.2 稳定可靠
- **生产验证**：基于已在生产环境验证的PSTS和GC代码
- **成熟机制**：复用经过长期优化的分配和回收逻辑
- **错误处理**：继承现有机制的完善错误处理

### 5.3 维护友好
- **统一框架**：与现有IPAM和GC框架保持一致
- **配置统一**：复用现有的配置管理机制
- **监控集成**：可以复用现有的监控和告警体系

### 5.4 性能优化
- **本地池缓存**：共享模式利用本地IP池提高分配性能
- **批量GC**：延迟删除机制减少频繁的云平台API调用
- **智能调度**：基于现有GC的智能调度机制

这个基于PSTS和GC机制的实现方案，通过最大化复用现有的成熟代码，实现了CrossVPC ENI的高效、可靠管理，同时大大降低了实现复杂度和维护成本。
