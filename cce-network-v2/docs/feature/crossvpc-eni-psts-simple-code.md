# CrossVPCEni 基于PSTS模式的代码实现示例

## 1. 核心数据结构

### 1.1 CCEEndpoint扩展

```go
// pkg/k8s/apis/cce.baidubce.com/v2/cce_endpoint_types.go
type IPAllocation struct {
    Type            IPAllocType               `json:"type,omitempty"`
    Pool            string                    `json:"pool,omitempty"`
    PSTSName        string                    `json:"pstsName,omitempty"`
    NodeName        string                    `json:"nodeName,omitempty"`
    ReleaseStrategy ReleaseStrategy           `json:"releaseStrategy,omitempty"`
    CrossVPCConfig  *CrossVPCAllocationConfig `json:"crossVPCConfig,omitempty"` // 新增
}

type CrossVPCAllocationConfig struct {
    UserID                           string   `json:"userID"`
    SubnetID                         string   `json:"subnetID"`
    SecurityGroupIDs                 []string `json:"securityGroupIDs"`
    VPCCIDR                          string   `json:"vpcCIDR"`
    Mode                             string   `json:"mode"` // "primary" | "secondary"
    PrivateIPAddress                 string   `json:"privateIPAddress,omitempty"`
    DefaultRouteInterfaceDelegation  string   `json:"defaultRouteInterfaceDelegation,omitempty"`
    DefaultRouteExcludedCidrs        []string `json:"defaultRouteExcludedCidrs,omitempty"`
}

// pkg/k8s/apis/cce.baidubce.com/v2/psts.go
const (
    IPAllocTypeNil                   IPAllocType = ""
    IPAllocTypeElastic               IPAllocType = "Elastic"
    IPAllocTypeFixed                 IPAllocType = "Fixed"
    IPAllocTypeENIPrimary            IPAllocType = "PrimaryENI"
    IPAllocTypeCrossVPCPrimary       IPAllocType = "CrossVPCPrimary"   // 新增
    IPAllocTypeCrossVPCSecondary     IPAllocType = "CrossVPCSecondary" // 新增
)
```

## 2. Agent侧实现

### 2.1 CrossVPC IPAM检测和处理

```go
// pkg/endpoint/agent_endpoint_allocator.go
func (e *endpointAllocator) allocateIP(ctx context.Context, logEntry *logrus.Entry, 
    containerID string, family ipam.Family, owner, netns string, 
    psts *ccev2.PodSubnetTopologySpread, pod *corev1.Pod, isFixedPod bool) (
    ipv4Result, ipv6Result *models.IPAMAddressResponse, err error) {
    
    // 检查是否为CrossVPC请求
    if isCrossVPCRequest(pod) {
        logEntry.Info("detected CrossVPC request")
        return e.allocateCrossVPCIP(ctx, logEntry, containerID, family, owner, netns, pod)
    }
    
    // 现有的PSTS和其他分配逻辑
    if psts != nil {
        return e.allocatePSTSIP(ctx, logEntry, containerID, family, owner, netns, psts, pod)
    }
    
    // 其他分配逻辑...
    return e.allocateRegularIP(ctx, logEntry, containerID, family, owner, netns, pod, isFixedPod)
}

func isCrossVPCRequest(pod *corev1.Pod) bool {
    enabled, ok := pod.Annotations["cross-vpc-eni.cce.io/enabled"]
    return ok && enabled == "true"
}

func (e *endpointAllocator) allocateCrossVPCIP(ctx context.Context, logEntry *logrus.Entry,
    containerID string, family ipam.Family, owner, netns string, pod *corev1.Pod) (
    ipv4Result, ipv6Result *models.IPAMAddressResponse, err error) {
    
    logEntry = logEntry.WithField("step", "allocateCrossVPCIP")
    
    // 1. 解析CrossVPC配置
    crossVPCConfig, err := parseCrossVPCAnnotations(pod.Annotations)
    if err != nil {
        logEntry.WithError(err).Error("failed to parse CrossVPC annotations")
        return nil, nil, fmt.Errorf("failed to parse CrossVPC annotations: %w", err)
    }
    
    logEntry = logEntry.WithFields(logrus.Fields{
        "crossvpc-mode":     crossVPCConfig.Mode,
        "crossvpc-userID":   crossVPCConfig.UserID,
        "crossvpc-subnetID": crossVPCConfig.SubnetID,
    })
    
    // 2. 创建CCEEndpoint模板
    ep := endpoint.NewEndpointTemplate(containerID, netns, pod)
    
    // 3. 设置CrossVPC分配类型
    allocType := ccev2.IPAllocTypeCrossVPCSecondary // 默认共享模式
    if crossVPCConfig.Mode == "primary" {
        allocType = ccev2.IPAllocTypeCrossVPCPrimary
    }
    
    ep.Spec.Network.IPAllocation = &ccev2.IPAllocation{
        Type:            allocType,
        NodeName:        nodeTypes.GetName(),
        ReleaseStrategy: ccev2.ReleaseStrategyTTL,
        CrossVPCConfig:  crossVPCConfig,
    }
    
    logEntry.WithField("allocType", allocType).Info("creating CCEEndpoint for CrossVPC")
    
    // 4. 创建endpoint，触发operator处理
    ep, err = e.cceEndpointClient.CCEEndpoints(ep.Namespace).Create(ctx, ep, metav1.CreateOptions{})
    if err != nil {
        logEntry.WithError(err).Error("failed to create CCEEndpoint")
        return nil, nil, fmt.Errorf("failed to create CCEEndpoint: %w", err)
    }
    
    // 5. 等待operator分配完成
    logEntry.Info("waiting for CrossVPC IP allocation")
    return e.waitForCrossVPCIPAllocation(ctx, ep, logEntry)
}

func parseCrossVPCAnnotations(annotations map[string]string) (*ccev2.CrossVPCAllocationConfig, error) {
    config := &ccev2.CrossVPCAllocationConfig{}
    
    // 解析必填字段
    requiredFields := map[string]*string{
        "cross-vpc-eni.cce.io/userID":           &config.UserID,
        "cross-vpc-eni.cce.io/subnetID":         &config.SubnetID,
        "cross-vpc-eni.cce.io/vpcCidr":          &config.VPCCIDR,
    }
    
    for annotation, field := range requiredFields {
        if value, ok := annotations[annotation]; ok {
            *field = value
        } else {
            return nil, fmt.Errorf("missing required annotation: %s", annotation)
        }
    }
    
    // 解析SecurityGroupIDs
    if sgIDs, ok := annotations["cross-vpc-eni.cce.io/securityGroupIDs"]; ok {
        config.SecurityGroupIDs = strings.Split(sgIDs, ",")
        // 清理空白字符
        for i, sgID := range config.SecurityGroupIDs {
            config.SecurityGroupIDs[i] = strings.TrimSpace(sgID)
        }
    } else {
        return nil, fmt.Errorf("missing required annotation: cross-vpc-eni.cce.io/securityGroupIDs")
    }
    
    // 解析可选字段
    config.Mode = annotations["cross-vpc-eni.cce.io/mode"]
    if config.Mode == "" {
        config.Mode = "secondary" // 默认共享模式
    }
    
    config.PrivateIPAddress = annotations["cross-vpc-eni.cce.io/privateIPAddress"]
    config.DefaultRouteInterfaceDelegation = annotations["cross-vpc-eni.cce.io/defaultRouteInterfaceDelegation"]
    
    if excludedCidrs, ok := annotations["cross-vpc-eni.cce.io/defaultRouteExcludedCidrs"]; ok {
        config.DefaultRouteExcludedCidrs = strings.Split(excludedCidrs, ",")
        // 清理空白字符
        for i, cidr := range config.DefaultRouteExcludedCidrs {
            config.DefaultRouteExcludedCidrs[i] = strings.TrimSpace(cidr)
        }
    }
    
    return config, nil
}

func (e *endpointAllocator) waitForCrossVPCIPAllocation(ctx context.Context, ep *ccev2.CCEEndpoint, logEntry *logrus.Entry) (
    ipv4Result, ipv6Result *models.IPAMAddressResponse, err error) {
    
    // 等待超时设置
    timeout := 5 * time.Minute
    timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()
    
    // 轮询检查endpoint状态
    ticker := time.NewTicker(2 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-timeoutCtx.Done():
            logEntry.Error("timeout waiting for CrossVPC IP allocation")
            return nil, nil, fmt.Errorf("timeout waiting for CrossVPC IP allocation")
            
        case <-ticker.C:
            // 获取最新的endpoint状态
            currentEP, err := e.cceEndpointClient.CCEEndpoints(ep.Namespace).Get(ctx, ep.Name, metav1.GetOptions{})
            if err != nil {
                logEntry.WithError(err).Debug("failed to get CCEEndpoint")
                continue
            }
            
            // 检查是否分配成功
            if len(currentEP.Status.Networking.Addressing) > 0 {
                logEntry.Info("CrossVPC IP allocation completed")
                return e.convertAddressingToIPAMResponse(currentEP.Status.Networking.Addressing)
            }
            
            // 检查是否有错误
            if currentEP.Status.State == ccev2.EndpointStateFailed {
                logEntry.Error("CrossVPC IP allocation failed")
                return nil, nil, fmt.Errorf("CrossVPC IP allocation failed: %s", currentEP.Status.Log)
            }
            
            logEntry.Debug("still waiting for CrossVPC IP allocation")
        }
    }
}

func (e *endpointAllocator) convertAddressingToIPAMResponse(addressing []*ccev2.AddressPair) (
    ipv4Result, ipv6Result *models.IPAMAddressResponse, err error) {
    
    for _, addr := range addressing {
        response := &models.IPAMAddressResponse{
            Address: &models.AddressPair{
                IPV4: addr.IP,
            },
            HostAddressing: &models.NodeAddressing{
                IPV4: &models.NodeAddressingElement{
                    Enabled: true,
                },
            },
            Interface: addr.Interface,
            Subnet:    addr.Subnet,
        }
        
        if addr.Family == ccev2.IPv4Family {
            ipv4Result = response
        } else if addr.Family == ccev2.IPv6Family {
            ipv6Result = response
        }
    }
    
    if ipv4Result == nil && ipv6Result == nil {
        return nil, nil, fmt.Errorf("no valid IP address found in addressing")
    }
    
    return ipv4Result, ipv6Result, nil
}
```

## 3. Operator侧实现

### 3.1 CrossVPC分配器提供者

```go
// pkg/endpoint/operator_crossvpc_manager_provider.go
package endpoint

import (
    "context"
    "fmt"
    "time"
    
    ccev2 "github.com/baidubce/baiducloud-cce-cni-driver/cce-network-v2/pkg/k8s/apis/cce.baidubce.com/v2"
    "github.com/sirupsen/logrus"
)

type crossVPCAllocatorProvider struct {
    *EndpointManager
}

func (provider *crossVPCAllocatorProvider) AllocateIP(ctx context.Context, log *logrus.Entry, resource *ccev2.CCEEndpoint) error {
    var (
        owner          = resource.Namespace + "/" + resource.Name
        status         = &resource.Status
        crossVPCConfig = resource.Spec.Network.IPAllocation.CrossVPCConfig
        allocType      = resource.Spec.Network.IPAllocation.Type
    )
    
    if crossVPCConfig == nil {
        return fmt.Errorf("CrossVPC config is required for CrossVPC allocation")
    }
    
    log = log.WithFields(logrus.Fields{
        "crossvpc-mode":     crossVPCConfig.Mode,
        "crossvpc-type":     allocType,
        "crossvpc-userID":   crossVPCConfig.UserID,
        "crossvpc-subnetID": crossVPCConfig.SubnetID,
        "endpoint":          owner,
    })
    log.Info("start allocate CrossVPC ENI")
    
    // 记录开始时间
    startTime := time.Now()
    
    // 获取远程操作接口
    operation, err := provider.directIPAllocator.NodeEndpoint(resource)
    if err != nil {
        log.WithError(err).Error("failed to get node endpoint")
        return fmt.Errorf("failed to get node endpoint: %w", err)
    }
    
    // 根据分配类型选择不同的处理逻辑
    switch allocType {
    case ccev2.IPAllocTypeCrossVPCPrimary:
        err = provider.allocateCrossVPCPrimary(ctx, log, resource, operation)
    case ccev2.IPAllocTypeCrossVPCSecondary:
        err = provider.allocateCrossVPCSecondary(ctx, log, resource, operation)
    default:
        err = fmt.Errorf("unsupported CrossVPC allocation type: %s", allocType)
    }
    
    // 记录分配结果
    duration := time.Since(startTime)
    if err != nil {
        log.WithError(err).WithField("duration", duration).Error("CrossVPC ENI allocation failed")
        // 记录监控指标
        RecordCrossVPCEniAllocation(resource.Spec.Network.IPAllocation.NodeName, 
            crossVPCConfig.Mode, crossVPCConfig.UserID, "failed", duration)
    } else {
        log.WithField("duration", duration).Info("CrossVPC ENI allocation succeeded")
        // 记录监控指标
        RecordCrossVPCEniAllocation(resource.Spec.Network.IPAllocation.NodeName, 
            crossVPCConfig.Mode, crossVPCConfig.UserID, "success", duration)
    }
    
    return err
}

// allocateCrossVPCPrimary 分配独占模式的CrossVPC ENI
func (provider *crossVPCAllocatorProvider) allocateCrossVPCPrimary(
    ctx context.Context, log *logrus.Entry, resource *ccev2.CCEEndpoint, 
    operation DirectEndpointOperation) error {
    
    var (
        status         = &resource.Status
        crossVPCConfig = resource.Spec.Network.IPAllocation.CrossVPCConfig
        owner          = resource.Namespace + "/" + resource.Name
    )
    
    log = log.WithField("step", "allocate crossvpc primary eni")
    
    // 构造分配动作
    action := &DirectIPAction{
        NodeName:    resource.Spec.Network.IPAllocation.NodeName,
        Owner:       owner,
        SubnetID:    crossVPCConfig.SubnetID,
        RequestedIP: crossVPCConfig.PrivateIPAddress,
        CrossVPCConfig: &DirectIPCrossVPCConfig{
            UserID:           crossVPCConfig.UserID,
            SecurityGroupIDs: crossVPCConfig.SecurityGroupIDs,
            VPCCIDR:          crossVPCConfig.VPCCIDR,
            Mode:             "primary",
            DefaultRouteInterfaceDelegation: crossVPCConfig.DefaultRouteInterfaceDelegation,
            DefaultRouteExcludedCidrs:       crossVPCConfig.DefaultRouteExcludedCidrs,
        },
    }
    
    log = log.WithField("action", fmt.Sprintf("%+v", action))
    
    // 调用远程分配
    err := operation.AllocateCrossVPCENI(ctx, action)
    if err != nil {
        log.WithError(err).Error("failed to allocate CrossVPC primary ENI")
        return fmt.Errorf("failed to allocate CrossVPC primary ENI: %w", err)
    }
    
    log.WithField("addressing", action.Addressing).Info("CrossVPC primary ENI allocated successfully")
    
    // 更新endpoint状态
    status.Networking.Addressing = action.Addressing
    status.NodeSelectorRequirement = action.NodeSelectorRequirement
    return nil
}

// allocateCrossVPCSecondary 分配共享模式的CrossVPC ENI
func (provider *crossVPCAllocatorProvider) allocateCrossVPCSecondary(
    ctx context.Context, log *logrus.Entry, resource *ccev2.CCEEndpoint,
    operation DirectEndpointOperation) error {
    
    var (
        status         = &resource.Status
        crossVPCConfig = resource.Spec.Network.IPAllocation.CrossVPCConfig
        owner          = resource.Namespace + "/" + resource.Name
    )
    
    log = log.WithField("step", "allocate crossvpc secondary ip")
    
    // 如果已经有地址，则复用（重启恢复场景）
    if len(status.Networking.Addressing) > 0 {
        log.Info("reuse existing CrossVPC secondary IP")
        return nil
    }
    
    // 构造分配动作
    action := &DirectIPAction{
        NodeName: resource.Spec.Network.IPAllocation.NodeName,
        Owner:    owner,
        SubnetID: crossVPCConfig.SubnetID,
        CrossVPCConfig: &DirectIPCrossVPCConfig{
            UserID:           crossVPCConfig.UserID,
            SecurityGroupIDs: crossVPCConfig.SecurityGroupIDs,
            VPCCIDR:          crossVPCConfig.VPCCIDR,
            Mode:             "secondary",
            DefaultRouteInterfaceDelegation: crossVPCConfig.DefaultRouteInterfaceDelegation,
            DefaultRouteExcludedCidrs:       crossVPCConfig.DefaultRouteExcludedCidrs,
        },
    }
    
    log = log.WithField("action", fmt.Sprintf("%+v", action))
    
    // 调用远程分配
    err := operation.AllocateCrossVPCENI(ctx, action)
    if err != nil {
        log.WithError(err).Error("failed to allocate CrossVPC secondary IP")
        return fmt.Errorf("failed to allocate CrossVPC secondary IP: %w", err)
    }
    
    log.WithField("addressing", action.Addressing).Info("CrossVPC secondary IP allocated successfully")
    
    // 更新endpoint状态
    status.Networking.Addressing = action.Addressing
    status.NodeSelectorRequirement = action.NodeSelectorRequirement
    return nil
}

var _ EndpointMunalAllocatorProvider = &crossVPCAllocatorProvider{}
```

## 4. DirectIPAction扩展

```go
// pkg/endpoint/operator_endpoint_manager.go
type DirectIPAction struct {
    // 现有字段
    NodeName                 string                          `json:"nodeName,omitempty"`
    Owner                    string                          `json:"owner,omitempty"`
    SubnetID                 string                          `json:"subnetID,omitempty"`
    Interface                string                          `json:"interface,omitempty"`
    Addressing               []*ccev2.AddressPair            `json:"addressing,omitempty"`
    NodeSelectorRequirement  []ccev2.NodeSelectorRequirement `json:"nodeSelectorRequirement,omitempty"`
    RequestedIP              string                          `json:"requestedIP,omitempty"`
    
    // 新增CrossVPC字段
    CrossVPCConfig           *DirectIPCrossVPCConfig         `json:"crossVPCConfig,omitempty"`
}

type DirectIPCrossVPCConfig struct {
    UserID                           string   `json:"userID"`
    SecurityGroupIDs                 []string `json:"securityGroupIDs"`
    VPCCIDR                          string   `json:"vpcCIDR"`
    Mode                             string   `json:"mode"` // "primary" | "secondary"
    DefaultRouteInterfaceDelegation  string   `json:"defaultRouteInterfaceDelegation,omitempty"`
    DefaultRouteExcludedCidrs        []string `json:"defaultRouteExcludedCidrs,omitempty"`
}

// DirectEndpointOperation 接口扩展
type DirectEndpointOperation interface {
    // 现有方法
    AllocateIP(ctx context.Context, action *DirectIPAction) error
    ReleaseIP(ctx context.Context, action *DirectIPAction) error
    FilterAvailableSubnetIds(subnetIDs []string, num int) []*BceSubnet
    
    // 新增CrossVPC方法
    AllocateCrossVPCENI(ctx context.Context, action *DirectIPAction) error
    ReleaseCrossVPCENI(ctx context.Context, action *DirectIPAction) error
}
```

## 5. EndpointManager集成

```go
// pkg/endpoint/operator_endpoint_manager.go
func NewEndpointManager(getterUpdater CCEEndpointGetterUpdater, reuseIPImplement DirectIPAllocator) *EndpointManager {
    manager := &EndpointManager{
        directIPAllocator:      reuseIPImplement,
        fixedIPPoolEndpointMap: make(map[string]map[string]*ccev2.CCEEndpoint),
        localPool:              newLocalPool(),
        remoteIPPool:           make(map[string]ipamTypes.AllocationMap),
        
        k8sAPI:        getterUpdater,
        pstsLister:    k8s.CCEClient().Informers.Cce().V2().PodSubnetTopologySpreads().Lister(),
        sbnLister:     k8s.CCEClient().Informers.Cce().V1().Subnets().Lister(),
        nrsLister:     k8s.CCEClient().Informers.Cce().V2().NetResourceSets().Lister(),
        eventRecorder: k8s.EventBroadcaster().NewRecorder(scheme.Scheme, corev1.EventSource{Component: operatorName}),
    }
    
    // 初始化分配器提供者
    manager.fixedIPProvider = &fixedIPAllocatorProvider{manager}
    manager.pstsAllocatorProvider = &pstsAllocatorProvider{manager}
    manager.crossVPCAllocatorProvider = &crossVPCAllocatorProvider{manager} // 新增
    
    return manager
}

func (manager *EndpointManager) Update(resource *ccev2.CCEEndpoint) error {
    // 检查是否为管理的endpoint类型
    if !IsFixedIPEndpoint(resource) && !IsPSTSEndpoint(resource) && !IsCrossVPCEndpoint(resource) {
        return nil
    }
    
    // 创建日志上下文
    logEntry := log.WithFields(logrus.Fields{
        "namespace": resource.Namespace,
        "name":      resource.Name,
        "step":      "Update",
    })
    
    ctx := context.Background()
    newObj := resource.DeepCopy()
    
    var err error
    
    // 根据类型分发到不同的分配器
    if IsCrossVPCEndpoint(resource) {
        logEntry.Info("processing CrossVPC endpoint")
        err = manager.crossVPCAllocatorProvider.AllocateIP(ctx, logEntry, newObj)
    } else if IsPSTSEndpoint(resource) {
        logEntry.Info("processing PSTS endpoint")
        err = manager.pstsAllocatorProvider.AllocateIP(ctx, logEntry, newObj)
    } else if IsFixedIPEndpoint(resource) {
        logEntry.Info("processing fixed IP endpoint")
        err = manager.fixedIPProvider.AllocateIP(ctx, logEntry, newObj)
    }
    
    // 更新endpoint状态
    if err != nil {
        logEntry.WithError(err).Error("failed to allocate IP")
        newObj.Status.State = ccev2.EndpointStateFailed
        newObj.Status.Log = err.Error()
    } else {
        logEntry.Info("IP allocation succeeded")
        newObj.Status.State = ccev2.EndpointStateReady
    }
    
    // 保存更新
    _, updateErr := manager.k8sAPI.Update(newObj)
    if updateErr != nil {
        logEntry.WithError(updateErr).Error("failed to update endpoint")
        return updateErr
    }
    
    return err
}

// IsCrossVPCEndpoint 判断是否为CrossVPC endpoint
func IsCrossVPCEndpoint(resource *ccev2.CCEEndpoint) bool {
    if resource.Spec.Network.IPAllocation == nil {
        return false
    }
    allocType := resource.Spec.Network.IPAllocation.Type
    return allocType == ccev2.IPAllocTypeCrossVPCPrimary || allocType == ccev2.IPAllocTypeCrossVPCSecondary
}
```

这个代码实现示例展示了如何基于PSTS模式实现CrossVPCEni的简洁流程：Agent检测并创建CEP → Operator异步处理 → Agent等待并返回结果。通过最大化复用现有的CCEEndpoint和EndpointManager框架，实现了高效的CrossVPC ENI分配和管理。
