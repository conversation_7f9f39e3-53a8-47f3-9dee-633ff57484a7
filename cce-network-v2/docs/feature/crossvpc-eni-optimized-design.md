# CrossVPCEni 优化设计方案

## 1. 核心优化点

### 1.1 Operator侧优化
- **原子化创建**：创建ENI时直接指定实例ID，实现创建+挂载原子化
- **错误码回写**：将云平台错误码回写到CEP，实现故障可运维
- **可重入设计**：支持幂等操作，避免故障导致的资源泄漏
- **异步处理**：使用delay event避免阻塞，提高并发性能
- **节点级IPAM**：每个节点维护用户级的IP池管理

### 1.2 Agent侧优化
- **类型区分**：独占和共享ENI使用不同的type标识
- **配置差异**：独占ENI无需初始化，共享ENI需要初始化配置

## 2. 独占模式流程

### 2.1 独占模式时序图

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant Agent as Agent
    participant CEP as CCEEndpoint
    participant Operator as Operator
    participant Cloud as Cloud API
    participant Queue as Event Queue

    Pod->>Agent: 创建Pod (mode=primary)
    Agent->>CEP: 创建CEP (type=CrossVPCPrimary)
    
    Note over Operator,Queue: 【可重入处理】
    Operator->>CEP: 检测CEP创建
    Operator->>Operator: 检查是否已有ENI CR
    
    alt ENI CR不存在
        Operator->>Cloud: 原子化创建ENI (指定instanceID)
        Note right of Cloud: CreateENI(instanceID, subnetID, ...)
        Cloud-->>Operator: 返回ENI ID (创建中状态)
        Operator->>CEP: 更新ENI ID到CEP
        Operator->>Queue: 返回delay event (1s后重试)
    else ENI CR存在
        Operator->>Cloud: 查询ENI状态
        alt ENI状态为inuse
            Operator->>CEP: 更新成功状态
        else ENI状态为creating/attaching
            Operator->>Queue: 返回delay event (1s后重试)
        else ENI状态为failed
            Operator->>CEP: 回写错误码
        end
    end
    
    Agent->>CEP: 检测到成功状态
    Agent->>Pod: 返回网络配置 (无需初始化)
```

### 2.2 独占模式伪代码

```go
// Operator处理独占模式
func handleCrossVPCPrimary(cep *CEP) (result, error) {
    // 1. 可重入检查
    if existingENI := findExistingENI(cep.Owner); existingENI != nil {
        status := checkENIStatus(existingENI.ID)
        switch status {
        case "inuse":
            cep.Status = "Ready"
            return Success, nil
        case "creating", "attaching":
            return DelayEvent(1s), nil
        case "failed":
            cep.Status = "Failed"
            cep.ErrorCode = getErrorCode(existingENI.ID)
            return Failed, nil
        }
    }
    
    // 2. 原子化创建ENI
    eniID := cloudAPI.CreateENI(
        instanceID: nodeID,
        subnetID: cep.SubnetID,
        securityGroups: cep.SecurityGroups,
    )
    
    // 3. 记录ENI信息
    cep.Status.ENIID = eniID
    cep.Status.State = "Creating"
    
    // 4. 返回延迟事件
    return DelayEvent(1s), nil
}

// Agent处理独占模式
func configureNetwork(cep *CEP) error {
    // 独占模式：ENI设备直接可用，无需初始化
    return configureDirectENI(cep.ENIID)
}
```

## 3. 共享模式流程

### 3.1 共享模式时序图

```mermaid
sequenceDiagram
    participant Pod as Pod
    participant Agent as Agent
    participant CEP as CCEEndpoint
    participant Operator as Operator
    participant IPAM as Node IPAM
    participant Cloud as Cloud API
    participant Queue as Event Queue

    Pod->>Agent: 创建Pod (mode=secondary)
    Agent->>CEP: 创建CEP (type=CrossVPCSecondary)
    
    Note over Operator,IPAM: 【节点级IPAM处理】
    Operator->>CEP: 检测CEP创建
    Operator->>IPAM: 请求分配IP (userID, subnetID)
    
    alt IPAM有可用IP
        IPAM-->>Operator: 返回IP地址
        Operator->>CEP: 更新IP信息
    else IPAM无可用IP
        Note over Operator,Cloud: 【可重入ENI创建】
        Operator->>Operator: 检查是否有创建中的ENI
        
        alt 无创建中的ENI
            Operator->>Cloud: 原子化创建共享ENI
            Cloud-->>Operator: 返回ENI ID
            Operator->>IPAM: 注册新ENI到IP池
            Operator->>Queue: 返回delay event (1s后重试)
        else 有创建中的ENI
            Operator->>Cloud: 查询ENI状态
            alt ENI就绪
                Operator->>IPAM: 将ENI加入IP池
                Operator->>IPAM: 分配IP
                IPAM-->>Operator: 返回IP地址
                Operator->>CEP: 更新IP信息
            else ENI创建中
                Operator->>Queue: 返回delay event (1s后重试)
            else ENI失败
                Operator->>CEP: 回写错误码
                Operator->>IPAM: 清理失败的ENI记录
            end
        end
    end
    
    Agent->>CEP: 检测到IP分配成功
    Agent->>Agent: 初始化共享ENI网络配置
    Agent->>Pod: 返回网络配置
```

### 3.2 共享模式伪代码

```go
// 节点级IPAM
type NodeIPAM struct {
    userPools map[string]*UserIPPool  // userID -> IP池
}

type UserIPPool struct {
    availableIPs []string
    enis         map[string]*ENIInfo
    maxIPsPerENI int
}

// Operator处理共享模式
func handleCrossVPCSecondary(cep *CEP) (result, error) {
    nodeIPAM := getNodeIPAM(cep.NodeName)
    
    // 1. 尝试从IPAM分配IP
    if ip := nodeIPAM.AllocateIP(cep.UserID, cep.SubnetID); ip != "" {
        cep.Status.IP = ip
        cep.Status.State = "Ready"
        return Success, nil
    }
    
    // 2. 可重入检查是否有创建中的ENI
    if creatingENI := nodeIPAM.GetCreatingENI(cep.UserID, cep.SubnetID); creatingENI != nil {
        status := checkENIStatus(creatingENI.ID)
        switch status {
        case "inuse":
            nodeIPAM.AddENIToPool(creatingENI)
            return DelayEvent(100ms), nil  // 快速重试分配IP
        case "creating":
            return DelayEvent(1s), nil
        case "failed":
            nodeIPAM.CleanupFailedENI(creatingENI.ID)
            cep.Status = "Failed"
            cep.ErrorCode = getErrorCode(creatingENI.ID)
            return Failed, nil
        }
    }
    
    // 3. 创建新的共享ENI
    eniID := cloudAPI.CreateENI(
        instanceID: nodeID,
        subnetID: cep.SubnetID,
        securityGroups: cep.SecurityGroups,
        maxSecondaryIPs: 8,
    )
    
    nodeIPAM.RegisterCreatingENI(cep.UserID, cep.SubnetID, eniID)
    return DelayEvent(1s), nil
}

// Agent处理共享模式
func configureNetwork(cep *CEP) error {
    // 共享模式：需要初始化网络配置
    return configureSecondaryIP(cep.IP, cep.ENIID)
}

// IPAM失败释放机制
func (pool *UserIPPool) ReleaseIP(ip string) {
    pool.availableIPs = append(pool.availableIPs, ip)
    
    // 检查ENI是否完全空闲
    if eni := pool.findENIByIP(ip); eni != nil && eni.isFullyIdle() {
        // 异步清理空闲ENI
        go pool.cleanupIdleENI(eni.ID)
    }
}
```

## 4. 错误处理优化

### 4.1 错误码回写机制

```mermaid
flowchart TD
    A[云平台API调用] --> B{调用结果}
    B -->|成功| C[更新CEP状态为Ready]
    B -->|失败| D[解析错误码]
    
    D --> E{错误类型}
    E -->|QuotaExceeded| F[CEP.ErrorCode = QUOTA_EXCEEDED]
    E -->|InvalidSubnet| G[CEP.ErrorCode = INVALID_SUBNET]
    E -->|InsufficientCapacity| H[CEP.ErrorCode = INSUFFICIENT_CAPACITY]
    E -->|ThrottlingException| I[返回DelayEvent重试]
    
    F --> J[CEP.Status = Failed]
    G --> J
    H --> K[返回DelayEvent重试]
    
    J --> L[Agent检测失败状态]
    L --> M[返回具体错误给Pod]
```

### 4.2 错误处理伪代码

```go
func handleCloudError(err error, cep *CEP) (result, error) {
    switch err.Code {
    case "QuotaExceeded":
        cep.Status.State = "Failed"
        cep.Status.ErrorCode = "QUOTA_EXCEEDED"
        cep.Status.ErrorMessage = "ENI quota exceeded"
        return Failed, nil
        
    case "InvalidSubnet":
        cep.Status.State = "Failed"
        cep.Status.ErrorCode = "INVALID_SUBNET"
        cep.Status.ErrorMessage = "Invalid subnet configuration"
        return Failed, nil
        
    case "InsufficientCapacity":
        cep.Status.ErrorCode = "INSUFFICIENT_CAPACITY"
        return DelayEvent(5s), nil  // 容量不足，延长重试间隔
        
    case "ThrottlingException":
        return DelayEvent(2s), nil  // API限流，短暂重试
        
    default:
        if isRetryable(err.Code) {
            return DelayEvent(1s), nil
        } else {
            cep.Status.State = "Failed"
            cep.Status.ErrorCode = err.Code
            cep.Status.ErrorMessage = err.Message
            return Failed, nil
        }
    }
}
```

## 5. 事件队列处理

### 5.1 Delay Event机制

```mermaid
stateDiagram-v2
    [*] --> Processing: CEP创建/更新
    Processing --> DelayQueue: 返回DelayEvent
    DelayQueue --> Processing: 延迟后重新处理
    Processing --> Success: 处理成功
    Processing --> Failed: 处理失败
    Success --> [*]
    Failed --> [*]
    
    note right of DelayQueue
        延迟时间：
        - 正常重试: 1s
        - API限流: 2s
        - 容量不足: 5s
    end note
```

### 5.2 事件处理伪代码

```go
// 复用现有的事件处理框架
func (c *Controller) processWorkItem() bool {
    obj, shutdown := c.workqueue.Get()
    if shutdown {
        return false
    }
    defer c.workqueue.Done(obj)
    
    result, err := c.handleCEP(obj.(*CEP))
    
    switch result {
    case Success:
        c.workqueue.Forget(obj)
        return true
        
    case Failed:
        c.workqueue.Forget(obj)
        return true
        
    case DelayEvent(duration):
        c.workqueue.AddAfter(obj, duration)  // 延迟重新加入队列
        return true
        
    default:
        c.workqueue.AddRateLimited(obj)  // 使用默认退避重试
        return true
    }
}
```

## 6. 核心优势

### 6.1 可靠性提升
- **原子化操作**：创建+挂载一步完成，减少中间状态
- **可重入设计**：支持故障恢复，避免资源泄漏
- **错误可观测**：错误码回写，便于运维排查

### 6.2 性能优化
- **异步处理**：使用delay event，避免阻塞
- **智能重试**：根据错误类型调整重试策略
- **资源复用**：节点级IPAM提高共享ENI利用率

### 6.3 运维友好
- **故障自愈**：可重入机制支持自动恢复
- **错误透明**：具体错误码便于问题定位
- **资源清理**：自动清理空闲资源，避免浪费

这个优化设计通过原子化操作、可重入处理、异步事件和节点级IPAM，显著提升了CrossVPC ENI的可靠性、性能和运维友好性。
